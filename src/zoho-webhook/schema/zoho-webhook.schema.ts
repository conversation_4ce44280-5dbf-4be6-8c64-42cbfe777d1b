import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { String } from 'aws-sdk/clients/codebuild';
import { Document, SchemaTypes, Types } from 'mongoose';

@Schema()
export class Address {
  @Prop() street: string;
  @Prop() addressLine1: string;
  @Prop() city: string;
  @Prop() state: string;
  @Prop() country: string;
}

@Schema()
export class MobileDetails {
  @Prop() countryCode: string;
  @Prop() number: string;
}

@Schema()
export class Minor {
  @Prop() firstName: string;
  @Prop() lastName: string;
  @Prop() dob: string;
  @Prop() gender: string;
  @Prop() mobile: string;
  @Prop() email: string;
  @Prop({ required: false, default: false }) isMinorAdded: boolean
}

@Schema({ timestamps: true })
export class ClientLead extends Document {
  @Prop() firstName: string;
  @Prop() lastName: string;
  @Prop() email: string;
  @Prop() phone: string;

  @Prop({ type: Address }) address: Address;
  @Prop({ type: Address }) businessAddress: Address;

  @Prop({ type: MobileDetails }) mobileDetails: MobileDetails;

  @Prop({ type: [Minor] }) minors: Minor[];

  @Prop({ type: Object, required: false }) rawZohoData: Record<string, any>;
  @Prop({ type: SchemaTypes.ObjectId, required: true, ref: "Facility" })
  facilityId: string;
  @Prop({ type: SchemaTypes.ObjectId, ref: "User", required: true })
  organizationId: string;
  @Prop({ type: Boolean, default: false })
  isConvertedToClient: boolean
  @Prop({ type: String, required: false })
  signature: String
}


export const ClientLeadSchema = SchemaFactory.createForClass(ClientLead);
