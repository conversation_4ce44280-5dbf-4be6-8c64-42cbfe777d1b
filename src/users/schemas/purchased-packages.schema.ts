import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
import { Document, SchemaTypes, Types } from "mongoose";
import { PaymentStatus } from "src/utils/enums/payment.enum";
import { SessionType } from "src/utils/enums/session-type.enum";

@Schema({ timestamps: true, versionKey: false })
export class Suspensions  extends Document{

    @Prop({ type: Boolean, required: false, default: undefined })
    isResumed?: boolean; // This is used to check if the suspension has been resumed, If it is not true and time elapsed then the package considered as resumed

    @Prop({ type: Date, required: true })
    fromDate: Date;

    @Prop({ type: Date, required: true })
    endDate: Date;

    @Prop({ type: String, required: false, default: "" })
    notes: string;
}
export const SuspensionsSchema = SchemaFactory.createForClass(Suspensions);

@Schema({ timestamps: true })
export class Purchase {

    @Prop({ type: SchemaTypes.ObjectId, required: false, ref: "User" })
    invoiceId: string;

    @Prop({ type: SchemaTypes.ObjectId, required: true, ref: "User" })
    userId: string;

    @Prop({ type: SchemaTypes.ObjectId, required: false, ref: "User", default: null })
    sponsorUser?: string;

    @Prop({ type: SchemaTypes.ObjectId, required: true, ref: 'Pricing' })
    packageId: string;

    @Prop({ type: SchemaTypes.ObjectId, required: false, ref: 'Pricing' })
    bundledPricingId: string;

    @Prop({ type: SchemaTypes.ObjectId, required: true, ref: "User" })
    organizationId: string;

    @Prop({ type: SchemaTypes.ObjectId, required: true, ref: 'Facility' })
    facilityId: string;

    @Prop({ type: SchemaTypes.ObjectId, required: true, ref: 'User' })
    purchasedBy: string;

    @Prop({ type: SchemaTypes.ObjectId, required: false, default: null, ref: 'membership' })
    membershipId: string;

    @Prop({ type: Date, required: true })
    purchaseDate: Date;

    @Prop({ type: String, required: true, enum: PaymentStatus })
    paymentStatus: PaymentStatus;

    @Prop({ type: Boolean, required: true })
    isExpired: boolean;

    @Prop({ type: Boolean, required: false, default: false })
    sharePass: boolean;

    @Prop({ type: SchemaTypes.ObjectId, required: false, ref: "User" })
    sharedBy: string;

    @Prop({ enum: SessionType, required: true })
    sessionType: SessionType;

    @Prop({ type: Number, required: true})
    totalSessions: number;

    @Prop({ type: Number, required: true, default: 0 })
    sessionConsumed?: number;

    @Prop({ type: Number, required: true, default: 0 })
    sessionShared?: number; // It is only to store how much session is shared 

    @Prop({ type: Number, required: false})
    sessionPerDay?: number;

    @Prop({ type: Number, required: false})
    dayPassLimit?: number;

    @Prop({ type: Boolean, required: false, default: true })
    isActive?: boolean;

    @Prop({ type: Date, required: true })
    startDate: Date;

    @Prop({ type: Date, required: true })
    endDate: Date;

    @Prop({ type: [SuspensionsSchema], required: false, default: [] })
    suspensions?: Suspensions[];

    @Prop({ type: String, required: false })
    qrCodeUrl: String;


}

export type PurchaseDocument = Purchase & Document;
export const PurchaseSchema = SchemaFactory.createForClass(Purchase);
