import { BadRequestException, Injectable, NotFoundException, InternalServerErrorException } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model, PipelineStage, Types } from "mongoose";
import { User } from "src/users/schemas/user.schema";
import { TransactionService } from "src/utils/services/transaction.service";
import { GeneralService } from "src/utils/services/general.service";
import { StaffProfileDetails } from "../schemas/staff.schema";
import { CreateStaffDto } from "../dto/create-staff-profile.dto";
import * as bcrypt from "bcrypt";
import { ConfigService } from "@nestjs/config";
import { MailService } from "src/mail/services/mail.service";
import { CompleteStaffProfileDto } from "../dto/complete-staff-profile.dto.ts";
import { updateStaffProfileDto } from "../dto/update-staff-profile.dto";
import { StaffPaginationDTO } from "../dto/pagination.dto";
import { MongoError } from "mongodb";
import { Facility } from "src/facility/schemas/facility.schema";
import { LoginWithPinDto } from "../dto/login-with-pin.dto";
import { JwtService } from "@nestjs/jwt";
import { Organizations } from "src/organization/schemas/organization.schema";
import { ResetPinDto } from "../dto/reset-pin.dto";
import { ScheduleDTO, StaffAvailabilityDto, TimeSlotsDTO } from "../dto/staff-availability.dto";
import { StaffAvailability } from "../schemas/staff-availability";
import { ListStaffAvailabilityByTypesDto, ListStaffAvailabilityDto } from "../dto/get-staff-availability.dto";
import { UpdateStaffAvailabilityDto } from "../dto/update-staff-availability.dto";
import { TrainersListDto } from "../dto/trainers-list.dto";
import { StaffPipe } from "../pipes/staff.pipe";
import { GetStaffAvailabilityDto } from "../dto/get-staff-details.dto";
import { FacilityClientListDto } from "../dto/facility-client-list.dto";
import { Clients } from "src/users/schemas/clients.schema";
import { DateRange } from "src/utils/enums/date-range-enum";
import { StaffAvailabilityEnum } from "src/utils/enums/staff-availability.enum";
import { DeleteStaffAvailabilityDto } from "../dto/delete-availability.dto";
import { FacilityAvailability } from "src/facility/schemas/facility-availability.schema";
import { DeleteStaffAvailability } from "src/utils/enums/delete-staff.enum";
import { Otp } from "src/auth/schemas/otp.schema";
import { AvailabilityServiceCategoryDto } from "../dto/avail-service-category.dto";
import { CheckMailorPhoneDto } from "../dto/checkMailorPhone.dto";
import { updateStaffStatusDto } from "src/staff/dto/update-staff-status.dto";
import { Scheduling } from "src/scheduling/schemas/scheduling.schema";
import { ScheduleStatusType } from "src/scheduling/enums/schedule-status.enum";
import { ClassType } from "src/utils/enums/class-type.enum";
import { PayRate } from "../schemas/pay-rate.schema";
import { dayAbbreviation } from "src/common/constants/dayAbbreviation";
import { AvailabilityType } from "src/facility/enums/availability-type.enum";
import { StaffAvailabilityByTypeDto } from "../dto/get-staff-availability-by-subtype.dto";
import { Services } from "src/organization/schemas/pricing.schema";
import moment from "moment";
import { ENUM_ROLE_TYPE } from "src/role/enums/role.enum";
import { IDatabaseObjectId } from "src/common/database/interfaces/database.objectid.interface";
import { ValidatePinDto } from "src/staff/dto/validate-pin.dto";
import { TrainersListV1Dto } from "../dto/trainer-listv1.dto";
import { TrainersListForPayRateDto } from "src/staff/dto/trainers-list-for-pay-rate.dto";
import { RoleService } from "src/role/services/role.service";
import { RoleTableName } from "src/role/repository/entities/role.entity";
import { MarkAvailabilityType } from "src/utils/enums/mark-availabilityType.enum";
import { Room } from "src/room/schema/room.schema";
import { IUserDocument } from "src/users/interfaces/user.interface";

@Injectable()
export class StaffService {
    readonly adminFrontEndHost = this.configService.getOrThrow<string>("ADMIN_FRONTEND_APP_URL");
    constructor(
        @InjectModel(User.name) private UserModel: Model<User>,
        @InjectModel(StaffProfileDetails.name) private StaffModel: Model<StaffProfileDetails>,
        @InjectModel(Facility.name) private FacilityModel: Model<Facility>,
        @InjectModel(Clients.name) private ClientModel: Model<Clients>,
        @InjectModel(Organizations.name) private OrganizationModel: Model<Organizations>,
        @InjectModel(StaffAvailability.name) private staffAvailabilityModel: Model<StaffAvailability>,
        @InjectModel(Otp.name) private OtpModel: Model<Otp>,
        @InjectModel(FacilityAvailability.name) private FacilityAvailabilityModel: Model<FacilityAvailability>,
        @InjectModel(PayRate.name) private PayRateModel: Model<PayRate>,
        @InjectModel(Scheduling.name) private SchedulingModel: Model<Scheduling>,
        @InjectModel(Services.name) private ServiceModel: Model<Services>,
        @InjectModel(Room.name) private readonly roomModel: Model<Room>,
        private readonly transactionService: TransactionService,
        private readonly generalService: GeneralService,
        private readonly configService: ConfigService,
        private readonly mailService: MailService,
        private readonly staffPipe: StaffPipe,
        private readonly roleService: RoleService,
        private JwtService: JwtService,
    ) { }

    async validatePin(loginWithPinDto: LoginWithPinDto, user: Object): Promise<any> {
        let facilitiesList = await this.FacilityModel.find({ organizationId: loginWithPinDto.organizationId }, { _id: 1 });
        if (facilitiesList.length == 0) {
            throw new BadRequestException("Facilities not found");
        }
        let facilityArray = [];
        facilityArray = facilitiesList.map((facility) => facility["_id"]);
        let checkPin = await this.StaffModel.findOne({ facilityId: { $in: facilityArray }, pin: loginWithPinDto.pin });
        if (!checkPin) throw new BadRequestException("Invalid pin");
        const accessToken = this.JwtService.sign({ userId: user["_id"] });
        return { user, accessToken: accessToken, organizationId: loginWithPinDto.organizationId };
    }

    async adminRegisterStaff(createStaffReq: CreateStaffDto, user: any, delegateUserId: any): Promise<any> {
        const session = await this.transactionService.startTransaction();
        try {
            const existingUser = await this.UserModel.findOne({
                $or: [{ email: createStaffReq.email }, { mobile: createStaffReq.mobile }],
            });

            if (existingUser) {
                throw new BadRequestException("Email or Mobile already exists");
            }

            const randomPassword = this.generalService.generateRandomPassword();
            const salt = await bcrypt.genSalt();
            const hashedPassword = await this.generalService.hashPassword(randomPassword, salt);
            const role = await this.roleService.findOneById(new Types.ObjectId(createStaffReq.role));
            if (!role) {
                throw new BadRequestException("Invalid role");
            }
            const staffDetails = {
                firstName: createStaffReq?.firstName,
                lastName: createStaffReq?.lastName || "",
                mobile: createStaffReq.mobile,
                email: createStaffReq.email,
                isActive: true,
                role: role._id,
                salt,
                password: hashedPassword,
            };
            const createdStaff = new this.UserModel(staffDetails);
            await createdStaff.save({ session });
            const userId = new Types.ObjectId(createdStaff._id);

            const staffProfileDetails = {
                gender: createStaffReq?.gender || "",
                facilityId: createStaffReq?.facilityId,
                setUpDate: createStaffReq?.setUpDate,
            };

            const staffId = this.generalService.generateStaffId();
            let staffData = {};
            let organizationSettings = await this.OrganizationModel.findOne({ userId: createStaffReq.organizationId }, { staffOnboarding: 1 });
            if (organizationSettings?.staffOnboarding?.["pin"]) {
                let isUnique = false;
                let uniqueNumber;
                while (!isUnique) {
                    // Generate a random 4-digit number
                    uniqueNumber = Math.floor(1000 + Math.random() * 9000);
                    // Check if the number already exists in the collection
                    const existingDoc = await this.StaffModel.findOne({ pin: uniqueNumber });
                    if (!existingDoc) {
                        isUnique = true;
                    }
                }
                staffData = {
                    createdBy: delegateUserId ?? user._id,
                    userId,
                    staffId,
                    pin: uniqueNumber,
                    organizationId: createStaffReq.organizationId,
                    ...staffProfileDetails,
                };
            } else {
                staffData = {
                    createdBy: delegateUserId ?? user._id,
                    userId,
                    staffId,
                    organizationId: createStaffReq.organizationId,
                    ...staffProfileDetails,
                };
            }

            const createdGymProfile = new this.StaffModel(staffData);
            await createdGymProfile.save({ session });
            const currentDate = new Date();
            if (createStaffReq.setUpDate <= currentDate) {
                // const token = jwt.sign({ email: createStaffReq.email.toString(), firstName: createStaffReq.firstName, lastName: createStaffReq.lastName }, process.env.JWT_SECRET);
                let randomNumber = await this.generalService.randomOTP();
                let otpDetails = new this.OtpModel({
                    otp: randomNumber,
                    for: createStaffReq.email.toString().toLowerCase(),
                });
                await otpDetails.save({ session });
                const passwordResetUrl = `${this.adminFrontEndHost}set-password?uid=${randomNumber}&id=${userId}&firstName=${createStaffReq.firstName}&lastName=${createStaffReq.lastName}&email=${createStaffReq.email.toLowerCase()}`;
                await this.mailService.sendMail({
                    to: createStaffReq?.email.toString(),
                    subject: `Welcome to ${user.name} - Set Your New Password`,
                    template: "password-reset",
                    context: {
                        name: user.name,
                        email: createStaffReq?.email,
                        dashboardUrl: passwordResetUrl,
                    },
                });
            }
            createdStaff.password = createdStaff.salt = undefined;
            await this.transactionService.commitTransaction(session);
            return {
                message: "Staff profile created successfully",
                data: createdStaff,
            };
        } catch (error) {
            await this.transactionService.abortTransaction(session);
            throw error;
        } finally {
            session.endSession();
        }
    }

    async resetPin(resetPinDto: ResetPinDto) {
        let isUnique = false;
        let uniqueNumber;

        while (!isUnique) {
            uniqueNumber = Math.floor(1000 + Math.random() * 9000);

            const existingDoc = await this.StaffModel.findOne({ pin: uniqueNumber });
            if (!existingDoc) {
                isUnique = true;
            }
        }

        let facilitiesList = await this.FacilityModel.find({ organizationId: resetPinDto.organizationId }, { _id: 1 });
        let facilityArray = facilitiesList.map((facility) => facility._id);
        let updatePin = await this.StaffModel.findOneAndUpdate(
            {
                userId: resetPinDto.userId,
                facilityId: { $in: facilityArray },
            },
            {
                pin: uniqueNumber,
            },
            {
                new: true,
            },
        );
        // if (!updatePin) throw new BadRequestException("Staff not found");
        return updatePin;
    }

    async adminUpdateStaffDetails(updateStaffDto: updateStaffProfileDto, userId: string): Promise<any> {
        const session = await this.transactionService.startTransaction();

        try {
            const { firstName, lastName, email, mobile, gender, dateOfBirth, address, certification, experience, description, profilePicture, facilityId, role } = updateStaffDto;

            const existingUser = await this.UserModel.findById(new Types.ObjectId(userId)).session(session);
            if (!existingUser) {
                throw new BadRequestException("User not found");
            }
            const userUpdate = {
                ...(firstName && { firstName }),
                ...(lastName && { lastName }),
                ...(email && { email }),
                ...(mobile && { mobile }),
                ...(role && { role }),
            };

            const updateUser = await this.UserModel.findOneAndUpdate({ _id: new Types.ObjectId(userId) }, { $set: userUpdate }, { new: true, upsert: true, session });
            const existingStaff = await this.StaffModel.findOne({ userId: new Types.ObjectId(userId) }).session(session);
            if (!existingStaff) {
                throw new BadRequestException("Staff record not found");
            }
            const staffUpdate = {
                ...(gender && { gender }),
                ...(dateOfBirth && { dateOfBirth }),
                ...(profilePicture && { profilePicture }),
                ...(facilityId && { facilityId }),
                address: {
                    stateId: address?.stateId ? new Types.ObjectId(address.stateId) : existingStaff.address?.stateId,
                    cityId: address?.cityId ? new Types.ObjectId(address.cityId) : existingStaff.address?.cityId,
                    street: address?.street ? address.street : existingStaff.address?.street,
                    country: address?.country ? address.country : existingStaff.address?.country,
                },
                ...(certification && { certification }),
                ...(experience && { experience }),
                ...(description && { description }),
            };

            const staff = await this.StaffModel.findOneAndUpdate({ userId: new Types.ObjectId(userId) }, { $set: staffUpdate }, { new: true, upsert: true, session });

            await session.commitTransaction();
            let response = {
                personalInfo: {
                    _id: updateUser._id,
                    firstName: updateUser.firstName,
                    lastName: updateUser.lastName,
                    isActive: updateUser.isActive,
                    role: updateUser.role,
                    email: updateUser.email,
                    mobile: updateUser.mobile,
                    profilePicture: staff.profilePicture,
                    gender: staff.gender,
                    dateOfBirth: staff.dateOfBirth,
                    address: staff.address,
                },
                additionalInfo: {
                    staffId: staff.staffId,
                    certification: staff.certification,
                    experience: staff.experience,
                    description: staff.description,
                    employmentDate: staff.setUpDate,
                },
            };

            return {
                message: "Staff Profile Updated Successfully",
                data: response,
            };
        } catch (error) {
            await this.transactionService.abortTransaction(session);

            if (error instanceof MongoError && error.code === 11000) {
                const field = Object.keys(error["keyValue"]);
                throw new Error(`User with this ${field} already exists`);
            }

            throw new BadRequestException(error.message || "An error occurred while updating the staff profile");
        } finally {
            session.endSession();
        }
    }

    async adminUpdateStaffStatus(updateStaffDto: updateStaffStatusDto, userId: string): Promise<any> {
        const session = await this.transactionService.startTransaction();

        try {
            const { isActive } = updateStaffDto;
            const existingUser = await this.UserModel.findById(new Types.ObjectId(userId)).session(session);
            if (!existingUser) {
                throw new BadRequestException("User not found");
            }

            const isStaffBooked = await this.SchedulingModel.findOne({ trainerId: new Types.ObjectId(userId) });
            if (isStaffBooked) {
                throw new BadRequestException("Booking is assigned to this user.");
            }
            const updateUser = await this.UserModel.findOneAndUpdate({ _id: new Types.ObjectId(userId) }, { $set: { isActive } }, { new: true, upsert: true, session });

            await session.commitTransaction();
            return {
                message: "Staff Status Updated Successfully",
                data: updateUser,
            };
        } catch (error) {
            await this.transactionService.abortTransaction(session);
            throw new BadRequestException(error.message || "An error occurred while updating the staff status");
        } finally {
            session.endSession();
        }
    }

    async completeStaffDetails(completeStaffDto: CompleteStaffProfileDto, user: any): Promise<any> {
        const session = await this.transactionService.startTransaction();

        try {
            const { firstName, lastName, gender, dateOfBirth, address, certification, experience, description, profilePicture } = completeStaffDto;

            const existingUser = await this.UserModel.findById(new Types.ObjectId(user._id)).session(session);
            if (!existingUser) {
                throw new BadRequestException("User not found");
            }

            const userUpdate = {
                ...(firstName && { firstName }),
                ...(lastName && { lastName }),
            };

            const updateUser = await this.UserModel.findOneAndUpdate({ _id: new Types.ObjectId(user._id) }, { $set: userUpdate }, { new: true, upsert: true, session });

            // Prepare staff update object
            const staffUpdate = {
                ...(gender && { gender }),
                ...(dateOfBirth && { dateOfBirth }),
                ...(profilePicture && { profilePicture }),
                address: {
                    ...(address.stateId && { stateId: new Types.ObjectId(address.stateId) }),
                    ...(address.cityId && { cityId: new Types.ObjectId(address.cityId) }),
                    ...(address.street && { street: address.street }),
                    ...(address.country && { country: address.country }),
                },
                ...(certification && { certification }),
                ...(experience && { experience }),
                ...(description && { description }),
            };

            const staff = await this.StaffModel.findOneAndUpdate({ userId: new Types.ObjectId(user._id) }, { $set: staffUpdate }, { new: true, upsert: true, session });

            await session.commitTransaction();
            let response = {
                personalInfo: {
                    _id: updateUser._id,
                    firstName: updateUser.firstName,
                    lastName: updateUser.lastName,
                    isActive: updateUser.isActive,
                    role: updateUser.role,
                    email: updateUser.email,
                    mobile: updateUser.mobile,
                    profilePicture: staff.profilePicture,
                    gender: staff.gender,
                    dateOfBirth: staff.dateOfBirth,
                    address: staff.address,
                },
                additionalInfo: {
                    staffId: staff.staffId,
                    certification: staff.certification,
                    experience: staff.experience,
                    description: staff.description,
                    employmentDate: staff.setUpDate,
                },
            };

            return {
                message: "Staff Profile Updated",
                data: response,
            };
        } catch (error) {
            await this.transactionService.abortTransaction(session);
            throw error.message;
        } finally {
            session.endSession();
        }
    }

    async adminDeleteStaff(userId: string): Promise<any> {
        const session = await this.transactionService.startTransaction();

        try {
            const staff = await this.StaffModel.findOne({ userId: new Types.ObjectId(userId) }).session(session);
            if (!staff) {
                throw new NotFoundException("Staff details not found");
            }

            await this.StaffModel.deleteOne({ userId: new Types.ObjectId(userId) }).session(session);
            await this.UserModel.deleteOne({ _id: new Types.ObjectId(userId) }).session(session);

            await session.commitTransaction();
            return { message: "Staff deleted successfully" };
        } catch (error) {
            await session.abortTransaction();
            throw new BadRequestException(error.message);
        } finally {
            session.endSession();
        }
    }

    async getAvailableFacility(userId: IDatabaseObjectId): Promise<any> {
        let facilityList = await this.StaffModel.findOne({ userId: userId }, { facilityId: 1 });
        if (!facilityList) throw new BadRequestException("Facilities not found");
        return facilityList["facilityId"];
    }

    async facilityListOrganization(userId: IDatabaseObjectId, locationId): Promise<any> {
        let query = { organizationId: userId };
        if (locationId.length > 0) {
            query["_id"] = { $in: locationId };
        }
        let facilityList = await this.FacilityModel.find(query, { _id: 1 });
        if (facilityList.length === 0) throw new BadRequestException("Facility not found");
        let facilityIds = facilityList.map((facility) => facility._id);
        return facilityIds;
    }

    async adminGetAllStaffList(body: StaffPaginationDTO, facilityList: Array<any>, locationId): Promise<any> {
        try {
            const allFacilityIds = [...new Set([...facilityList, ...locationId].map((id) => id.toString()))].map((id) => new Types.ObjectId(id));
            let { page, pageSize, search, role, isActive } = body;

            page = isNaN(page) || page < 1 ? 1 : page;
            pageSize = isNaN(pageSize) || pageSize < 1 ? 10 : pageSize;
            const skip = (page - 1) * pageSize;
            let query: any = {};

            const staffRoles = body.role ?? [ENUM_ROLE_TYPE.TRAINER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.WEB_MASTER]
            const roles = await this.roleService.findAll({ type: { $in: staffRoles } }, { select: '_id' })
            const roleId = roles.map((role) => role._id);
            query["role"] = { $in: roleId };

            let searchQuery: any = [];

            if (search?.length > 0) {
                searchQuery.push(
                    { firstName: { $regex: search, $options: "i" } },
                    { lastName: { $regex: search, $options: "i" } },
                    { mobile: { $regex: search, $options: "i" } },
                    { email: { $regex: search, $options: "i" } },
                );
            }
            if (searchQuery.length > 0) {
                query["$or"] = searchQuery;
            }
            if (isActive) {
                query["isActive"] = isActive
            }
            const commonPipeline: any[] = [
                { $match: query },
                { $sort: { createdAt: -1 } },
                {
                    $lookup: {
                        from: "staffprofiledetails",
                        localField: "_id",
                        foreignField: "userId",
                        as: "staffDetails",
                        pipeline: [{ $match: { facilityId: { $in: allFacilityIds } } }],
                    },
                },
                { $unwind: { path: "$staffDetails", preserveNullAndEmptyArrays: false } },
                {
                    $lookup: {
                        from: "facilities",
                        localField: "staffDetails.facilityId",
                        foreignField: "_id",
                        as: "facilityDetails",
                    },
                },
                { $unwind: { path: "$facilityDetails", preserveNullAndEmptyArrays: true } },
                {
                    $lookup: {
                        from: RoleTableName,
                        localField: "role",
                        foreignField: "_id",
                        as: "roleDetails",
                    },
                },
                {
                    $set: {
                        role: { $first: "$roleDetails.type" },
                    }
                }
            ];

            // Add conditional grouping and filtering based on locationId presence
            if (locationId?.length > 0) {
                commonPipeline.push(
                    {
                        $group: {
                            _id: "$staffDetails._id",
                            gender: { $first: "$staffDetails.gender" },
                            profilePicture: { $first: "$staffDetails.profilePicture" },
                            userId: { $first: "$_id" },
                            firstName: { $first: "$firstName" },
                            lastName: { $first: "$lastName" },
                            mobile: { $first: "$mobile" },
                            email: { $first: "$email" },
                            role: { $first: "$role" },
                            isActive: { $first: "$isActive" },
                            createdAt: { $first: "$createdAt" },
                            facilityNames: {
                                $push: {
                                    $cond: {
                                        if: { $in: ["$facilityDetails._id", allFacilityIds] },
                                        then: "$facilityDetails.facilityName",
                                        else: null,
                                    },
                                },
                            },
                        },
                    },
                    {
                        $addFields: {
                            facilityNames: {
                                $filter: {
                                    input: "$facilityNames",
                                    as: "name",
                                    cond: { $ne: ["$$name", null] }, // Remove any null entries from facilityNames
                                },
                            },
                        },
                    },
                );
            } else {
                commonPipeline.push({
                    $group: {
                        _id: "$staffDetails._id",
                        gender: { $first: "$staffDetails.gender" },
                        profilePicture: { $first: "$staffDetails.profilePicture" },
                        userId: { $first: "$_id" },
                        firstName: { $first: "$firstName" },
                        lastName: { $first: "$lastName" },
                        mobile: { $first: "$mobile" },
                        email: { $first: "$email" },
                        role: { $first: "$role" },
                        isActive: { $first: "$isActive" },
                        createdAt: { $first: "$createdAt" },
                        facilityNames: { $push: "$facilityDetails.facilityName" },
                    },
                });
            }

            // Add pagination and final sorting
            commonPipeline.push(
                {
                    $sort: { createdAt: -1 },
                },
                {
                    $facet: {
                        metadata: [{ $count: "total" }],
                        data: [
                            {
                                $sort: {
                                    isActive: -1,
                                    firstName: 1
                                }
                            },
                            { $skip: skip },
                            { $limit: pageSize },
                            {
                                $project: {
                                    _id: 1,
                                    userId: 1,
                                    firstName: 1,
                                    lastName: 1,
                                    facilityNames: 1,
                                    mobile: 1,
                                    email: 1,
                                    role: 1,
                                    isActive: 1,
                                    createdAt: 1,
                                    gender: 1,
                                    profilePicture: 1,
                                },
                            },
                        ],
                    },
                },
            );

            // Execute aggregation
            const result = await this.UserModel.aggregate(commonPipeline);

            const totalUsers = result[0].metadata[0] ? result[0].metadata[0].total : 0;
            const totalPages = Math.ceil(totalUsers / pageSize);

            return {
                message: result[0].data.length > 0 ? "Staff list fetched successfully" : "Staff list not found",
                data: result[0].data,
                pagination: {
                    currentPage: page,
                    currentPageSize: pageSize,
                    totalPages: totalPages,
                    totalCount: totalUsers,
                },
            };
        } catch (error) {
            throw new Error(error.message);
        }
    }

    async adminGetStaffDetailsById(userId: string): Promise<any> {
        try {
            const id = new Types.ObjectId(userId);
            const user = await this.UserModel.aggregate([
                {
                    $match: {
                        _id: id,
                    },
                },
                {
                    $lookup: {
                        from: "staffprofiledetails",
                        localField: "_id",
                        foreignField: "userId",
                        as: "staffDetails",
                    },
                },
                {
                    $unwind: "$staffDetails",
                },
                {
                    $lookup: {
                        from: "facilities",
                        localField: "staffDetails.facilityId",
                        foreignField: "_id",
                        as: "facilityInfo",
                    },
                },
                {
                    $unwind: "$facilityInfo",
                },
                {
                    $lookup: {
                        from: "cities",
                        localField: "facilityInfo.address.city",
                        foreignField: "_id",
                        as: "cityDetails",
                    },
                },
                {
                    $unwind: "$cityDetails",
                },
                {
                    $lookup: {
                        from: RoleTableName,
                        localField: "role",
                        foreignField: "_id",
                        as: "roleDetails",
                        pipeline: [
                            {
                                $project: {
                                    type: 1,
                                    _id: 1,
                                },
                            },
                        ],
                    },
                },
                {
                    $unwind: "$roleDetails",
                },
                {
                    $set: {
                        roleType: "$roleDetails.type",
                        role: "$roleDetails._id",
                    },
                },
                {
                    $group: {
                        _id: "$_id",
                        personalInfo: { $first: "$$ROOT" },
                        cityNames: { $push: "$cityDetails.name" },
                        cityIds: { $push: "$cityDetails._id" },
                    },
                },
                {
                    $project: {
                        _id: 0,
                        personalInfo: {
                            _id: "$personalInfo._id",
                            firstName: "$personalInfo.firstName",
                            lastName: "$personalInfo.lastName",
                            isActive: "$personalInfo.isActive",
                            role: "$personalInfo.role",
                            roleType: "$personalInfo.roleType",
                            email: "$personalInfo.email",
                            mobile: "$personalInfo.mobile",
                            profilePicture: "$personalInfo.staffDetails.profilePicture",
                            gender: "$personalInfo.staffDetails.gender",
                            dateOfBirth: "$personalInfo.staffDetails.dateOfBirth",
                            address: "$personalInfo.staffDetails.address",
                        },
                        additionalInfo: {
                            staffId: "$personalInfo.staffDetails.staffId",
                            certification: "$personalInfo.staffDetails.certification",
                            experience: "$personalInfo.staffDetails.experience",
                            description: "$personalInfo.staffDetails.description",
                            employmentDate: "$personalInfo.staffDetails.setUpDate",
                        },
                        facilityInfo: {
                            facilityId: "$personalInfo.staffDetails.facilityId",
                            cityNames: "$cityNames",
                            cityIds: "$cityIds",
                        },
                    },
                },
            ]);

            if (user.length === 0) {
                return { message: "Staff details not found", data: [] };
            }
            return { message: "Staff details fetched successfully", data: user[0] };
        } catch (error) {
            throw new Error(error.message);
        }
    }

    async resetPassword(userId: string, oldPassword: string, newPassword: string, confirmPassword: string): Promise<any> {
        const user = await this.UserModel.findById(new Types.ObjectId(userId));
        if (!user) {
            throw new BadRequestException("User not found");
        }

        const isMatch = await bcrypt.compare(oldPassword, user.password);
        if (!isMatch) {
            throw new BadRequestException("Old password is incorrect");
        }

        if (newPassword !== confirmPassword) {
            throw new BadRequestException("New password and confirm password do not match");
        }

        const salt = await bcrypt.genSalt();
        const password = await this.generalService.hashPassword(newPassword, salt);

        user.salt = salt;
        user.password = password;

        await user.save();

        return { message: "Password reset successfully" };
    }

    async trainerClientList(facilityClientListDto: FacilityClientListDto, user: Object): Promise<any> {
        let joinedFacilities = await this.StaffModel.findOne({ userId: user["_id"] }, { facilityId: 1 });
        if (!joinedFacilities) throw new BadRequestException("Trainer not found");

        let facilitiesId = joinedFacilities["facilityId"];
        let pipeline = this.staffPipe.getTrainerClientList(facilityClientListDto, facilitiesId);
        let data = await this.ClientModel.aggregate(pipeline);
        return {
            list: data[0]?.list,
            count: data[0]?.total[0]?.total ? data[0]?.total[0]?.total : 0,
        };
    }

    async trainersListOrg(trainerListDto: TrainersListDto, organizationId: string): Promise<any> {
        let facilitiesArray = [];
        if (!trainerListDto.facilityId) {
            let facilitiesInOrg = await this.FacilityModel.find({ organizationId: organizationId }, { _id: 1 });
            facilitiesArray = facilitiesInOrg.map((ele) => ele._id);
        } else {
            facilitiesArray = [Types.ObjectId.createFromHexString(trainerListDto.facilityId)];
        }
        let pipeline = this.staffPipe.getStaffListPipe(trainerListDto, facilitiesArray);
        let result = await this.StaffModel.aggregate(pipeline);
        return result;
    }

    async trainersListStaff(trainerListDto: TrainersListDto, staffId: string): Promise<any> {
        let facilityId = []
        if (!trainerListDto.facilityId) {
            const staff = await this.StaffModel.findOne({ userId: staffId }, { facilityId: 1 });
            facilityId = [...facilityId, staff.facilityId]

        }
        else {
            facilityId = [Types.ObjectId.createFromHexString(trainerListDto.facilityId)]
        }
        let pipeline = this.staffPipe.getStaffListPipe(trainerListDto, facilityId);
        let result = await this.StaffModel.aggregate(pipeline);
        return result;
    }

    async trainersListForPayRate(trainerListDto: TrainersListForPayRateDto, user: IUserDocument): Promise<any> {
        const { role, _id: userId } = user;
        let facilitiesArray = [];

        if (role.type === ENUM_ROLE_TYPE.ORGANIZATION) {
            const facilitiesInOrg = await this.FacilityModel.find(
                { organizationId: trainerListDto.organizationId },
                { _id: 1 }
            ).lean();
            facilitiesArray = facilitiesInOrg.map((ele) => ele._id);
        } else if (role.type === ENUM_ROLE_TYPE.WEB_MASTER || role.type === ENUM_ROLE_TYPE.FRONT_DESK_ADMIN || role.type === ENUM_ROLE_TYPE.TRAINER) {
            const staffList = await this.StaffModel.find(
                { userId, organizationId: trainerListDto.organizationId },
                { facilityId: 1 }
            ).lean();

            if (!staffList.length) throw new BadRequestException("Access denied");

            facilitiesArray = staffList.map((ele) => ele.facilityId);
        }

        const payRateData = await this.PayRateModel.find(
            {
                organizationId: trainerListDto.organizationId,
                serviceType: trainerListDto.serviceType,
                serviceCategory: trainerListDto.serviceCategory,
                appointmentType: trainerListDto.appointmentType,
            },
            { userId: 1 }
        ).lean()

        const excludedTrainers = payRateData.map((data) => data.userId);
        let pipeline = this.staffPipe.getStaffListPipev1(trainerListDto, facilitiesArray, excludedTrainers);
        let result = await this.StaffModel.aggregate(pipeline);
        return result;
    }

    private isFromLessThanTo(slot: TimeSlotsDTO): boolean {
        const fromDate = new Date(`1970-01-01T${slot.from}:00`);
        const toDate = new Date(`1970-01-01T${slot.to}:00`);
        return fromDate < toDate;
    }

    private validateTimeSlots(schedule): void {
        const days = ["mon", "tue", "wed", "thu", "fri", "sat", "sun"];

        for (const day of days) {
            const daySlots: TimeSlotsDTO[] = schedule[day];
            if (daySlots && daySlots.length > 0) {
                for (const slot of daySlots) {
                    if (!this.isFromLessThanTo(slot)) {
                        throw new BadRequestException(`Invalid time range for ${day}: 'from' time (${slot.from}) must be less than 'to' time (${slot.to})`);
                    }
                }

                for (let i = 0; i < daySlots.length; i++) {
                    for (let j = i + 1; j < daySlots.length; j++) {
                        if (this.isOverlapping(daySlots[i], daySlots[j])) {
                            throw new BadRequestException(`Time slots overlap on ${day}!`);
                        }
                    }
                }
            }
        }
    }

    private isOverlapping(slot1: TimeSlotsDTO, slot2: TimeSlotsDTO): boolean {
        return slot1.from < slot2.to && slot2.from < slot1.to;
    }

    private isTimeOverlapping(slot1: { from: string; to: string }, slot2: { from: String; to: String }): boolean {
        return slot1.from < slot2.to && slot1.to > slot2.from;
    }

    private isTimeWithinAvailableSlot(requestedSlot: { from: string; to: string }, availableSlot: { from: string; to: string }): boolean {
        return requestedSlot.from >= availableSlot.from && requestedSlot.to <= availableSlot.to;
    }

    private transformTimeSlotsForSingle(scheduleDto, availabilityStatus: string, startDate: Date, classType, privacy?: string, reason?: string) {
        const daysOfWeek = ["sun", "mon", "tue", "wed", "thu", "fri", "sat"];
        const transformedTimeSlots = [];

        const dayIndex = new Date(startDate).getDay();
        const selectedDay = daysOfWeek[dayIndex];

        const daySlots = scheduleDto[selectedDay as keyof ScheduleDTO];
        if (daySlots && daySlots.length > 0) {
            daySlots.forEach((slot) => {
                const timeSlot: any = {
                    from: slot.from,
                    to: slot.to,
                    availabilityStatus: availabilityStatus,
                };

                // Conditionally add keys for AVAILABLE status
                if (availabilityStatus === StaffAvailabilityEnum.AVAILABLE) {
                    timeSlot.classType = classType;
                    timeSlot.payRateIds = slot?.payRateIds.map((id) => new Types.ObjectId(id));
                }

                // Add optional keys
                if (privacy) {
                    timeSlot.privacy = privacy;
                }
                if (availabilityStatus === StaffAvailabilityEnum.UNAVAILABLE && reason) {
                    timeSlot.reason = reason;
                }

                transformedTimeSlots.push(timeSlot);
            });
        }

        return transformedTimeSlots;
    }

    private transformTimeSlotsForMultiple(scheduleDto, availabilityStatus: string, classType, privacy?: string, reason?: string) {
        let transformedTimeSlots = [];

        if (scheduleDto && scheduleDto.length > 0) {
            scheduleDto.forEach((slot) => {
                const timeSlot: any = {
                    from: slot.from,
                    to: slot.to,
                    availabilityStatus: availabilityStatus,
                };

                // Conditionally add keys for AVAILABLE status
                if (availabilityStatus === StaffAvailabilityEnum.AVAILABLE) {
                    timeSlot.classType = classType;
                    timeSlot.payRateIds = slot?.payRateIds.map((id) => new Types.ObjectId(id));
                }

                // Add optional keys
                if (privacy) {
                    timeSlot.privacy = privacy;
                }
                if (availabilityStatus === StaffAvailabilityEnum.UNAVAILABLE && reason) {
                    timeSlot.reason = reason;
                }

                transformedTimeSlots.push(timeSlot);
            });
        }
        return transformedTimeSlots;
    }

    private mergeOrSplitTimeSlots(existingSlots: any[], newSlots: any[]): any[] {
        const mergedSlots = [...existingSlots];

        newSlots.forEach((newSlot) => {
            let isMerged = false;
            for (let i = 0; i < mergedSlots.length; i++) {
                const existingSlot = mergedSlots[i];

                if (this.isOverlapping(existingSlot, newSlot)) {
                    if (existingSlot.availabilityStatus === newSlot.availabilityStatus) {
                        existingSlot.from = existingSlot.from > newSlot.from ? newSlot.from : existingSlot.from;
                        existingSlot.to = existingSlot.to > newSlot.to ? existingSlot.to : newSlot.to;
                        existingSlot.payRateIds = newSlot.payRateIds;
                        isMerged = true;
                        break;
                    } else {
                        if (newSlot.from > existingSlot.from && newSlot.to < existingSlot.to) {
                            mergedSlots.push({ ...existingSlot, to: newSlot.from });
                            mergedSlots.push({ ...newSlot });
                            mergedSlots.push({ ...existingSlot, from: newSlot.to });
                        } else if (newSlot.from <= existingSlot.from && newSlot.to < existingSlot.to) {
                            mergedSlots.push({ ...newSlot });
                            mergedSlots.push({ ...existingSlot, from: newSlot.to });
                        } else if (newSlot.from > existingSlot.from && newSlot.to >= existingSlot.to) {
                            mergedSlots.push({ ...existingSlot, to: newSlot.from });
                            mergedSlots.push({ ...newSlot });
                        } else {
                            mergedSlots.push({ ...newSlot });
                        }
                        mergedSlots.splice(i, 1);
                        isMerged = true;
                        break;
                    }
                }
            }
            if (!isMerged) {
                mergedSlots.push(newSlot);
            }
        });

        // Sort the slots by start time
        mergedSlots.sort((a, b) => a.from.localeCompare(b.from));

        return mergedSlots;
    }


    private async mergeOrSplitTimeSlotsOnUpdate(existingSlots: any[], newSlots: any[], startTime?: string): Promise<any[]> {
        type TimeSlot = {
            from: string;
            to: string;
            availabilityStatus: string;
            reason?: string;
            privacy?: string;
            classType?: string;
            payRateIds?: string[];
            _id?: any;
            isNew?: boolean;
            [key: string]: any;
        };

        class TimeSlotManager {
            isOverlapping(a: TimeSlot, b: TimeSlot): boolean {
                return a.from < b.to && a.to > b.from;
            }

            private mergeTimeSlots(slots: TimeSlot[]): TimeSlot[] {
                if (slots.length === 0) return [];
                const merged: TimeSlot[] = [];
                let current = { ...slots[0] };
                for (let i = 1; i < slots.length; i++) {
                    const next = slots[i];
                    const isAdjacent = current.to === next.from;
                    const sameAvailability = current.availabilityStatus === next.availabilityStatus;
                    const samePayRates = JSON.stringify(current.payRateIds) === JSON.stringify(next.payRateIds);
                    if (isAdjacent && sameAvailability && samePayRates) {
                        current.to = next.to;
                    } else {
                        merged.push(current);
                        current = { ...next };
                    }
                }
                merged.push(current);
                return merged;
            }

            async updateTimeSlots(existingSlots: TimeSlot[], newSlotsInput: TimeSlot[], startTime: string | null = null): Promise<TimeSlot[]> {
                let updatedSlots = Array.isArray(existingSlots)
                    ? JSON.parse(JSON.stringify(existingSlots)).map(s => ({ ...s, isNew: false }))
                    : [];

                if (startTime !== null) {
                    updatedSlots = updatedSlots.filter(slot => slot.from !== startTime);
                }

                if (Array.isArray(newSlotsInput)) {
                    newSlotsInput.forEach(ns => {
                        if (ns && typeof ns.from === 'string' && typeof ns.to === 'string' && ns.from < ns.to) {
                            updatedSlots.push({
                                ...ns,
                                _id: new Types.ObjectId().toString(),
                                isNew: true
                            });
                        }
                    });
                }

                let iterationSafetyCounter = 0;
                const MAX_ITERATIONS = Math.max(10, (updatedSlots.length + (Array.isArray(newSlotsInput) ? newSlotsInput.length : 0)) * 5);

                let madeChangeInPass;
                do {
                    madeChangeInPass = false;
                    iterationSafetyCounter++;

                    if (iterationSafetyCounter > MAX_ITERATIONS) {
                        break;
                    }

                    updatedSlots.sort((a, b) => {
                        if (a.from !== b.from) return a.from.localeCompare(b.from);
                        if (a.to !== b.to) return a.to.localeCompare(b.to);
                        if (a.isNew && !b.isNew) return -1;
                        if (!a.isNew && b.isNew) return 1;
                        return 0;
                    });

                    const originalLength = updatedSlots.length;
                    updatedSlots = updatedSlots.filter(slot => slot.from < slot.to);
                    if (updatedSlots.length < originalLength) {
                        madeChangeInPass = true;
                    }

                    for (let i = 0; i < updatedSlots.length - 1; i++) {
                        const current = updatedSlots[i];
                        const next = updatedSlots[i + 1];

                        if (this.isOverlapping(current, next)) {
                            madeChangeInPass = true;

                            const currentIsNew = current.isNew;
                            const nextIsNew = next.isNew;

                            if (!currentIsNew && nextIsNew) {
                                const originalCurrentTo = current.to;
                                if (current.from < next.from) {
                                    current.to = next.from;
                                } else {
                                    updatedSlots.splice(i, 1);
                                    break;
                                }
                                if (originalCurrentTo > next.to) {
                                    const tailFragment = {
                                        ...current,
                                        from: next.to,
                                        to: originalCurrentTo,
                                        _id: new Types.ObjectId().toString(),
                                        isNew: false
                                    };
                                    updatedSlots.splice(i + 2, 0, tailFragment);
                                    break;
                                }
                                break;
                            } else if (currentIsNew && !nextIsNew) {
                                const originalNextFrom = next.from;
                                const originalNextTo = next.to;
                                if (next.to > current.to) {
                                    next.from = current.to;
                                } else {
                                    updatedSlots.splice(i + 1, 1);
                                    break;
                                }
                                if (originalNextFrom < current.from) {
                                    const headFragment = {
                                        ...next,
                                        from: originalNextFrom,
                                        to: current.from,
                                        payRateIds: JSON.parse(JSON.stringify(next.payRateIds)),
                                        availabilityStatus: next.availabilityStatus,
                                        _id: new Types.ObjectId().toString(),
                                        isNew: false
                                    };
                                    updatedSlots.splice(i, 0, headFragment);
                                    break;
                                }
                                break;
                            } else {
                                if (currentIsNew) {
                                    if (current.to >= next.to) {
                                        updatedSlots.splice(i + 1, 1);
                                    } else {
                                        next.from = current.to;
                                    }
                                } else {
                                    const canMerge =
                                        current.availabilityStatus === next.availabilityStatus &&
                                        JSON.stringify(current.payRateIds) === JSON.stringify(next.payRateIds);

                                    if (canMerge && current.to >= next.from) {
                                        current.to = current.to > next.to ? current.to : next.to;
                                        updatedSlots.splice(i + 1, 1);
                                    } else {
                                        if (current.to >= next.to) {
                                            updatedSlots.splice(i + 1, 1);
                                        } else {
                                            next.from = current.to;
                                        }
                                    }
                                }
                                break;
                            }
                        }
                    }
                } while (madeChangeInPass && iterationSafetyCounter <= MAX_ITERATIONS);

                updatedSlots.forEach(slot => delete slot.isNew);
                updatedSlots = updatedSlots.filter(slot => slot.from < slot.to);
                updatedSlots.sort((a, b) => a.from.localeCompare(b.from));

                return this.mergeTimeSlots(updatedSlots)
                    .map(slot => {
                        const cleaned: any = {};
                        Object.entries(slot).forEach(([key, value]) => {
                            if (value !== undefined) {
                                // Ensure _id is always a MongoDB ObjectId
                                if (key === "_id" && typeof value === "string") {
                                    cleaned._id = new Types.ObjectId(value);
                                } else {
                                    cleaned[key] = value;
                                }
                            }
                        });
                        return cleaned;
                    });
            }
        }

        const manager = new TimeSlotManager();
        return manager.updateTimeSlots(existingSlots, newSlots, startTime);
    }

    private async mergeOrSplitTimeSlotsOnUpdateV0<T>(startTime: string, endTime: string, existingSlots: any[], newSlots: any[]): Promise<any[T]> {

        for (const newSlot of newSlots) {
            for (const existingSlot of existingSlots) {
                // Skip validation for slots exactly matching start and end times
                if (
                    (existingSlot.from === startTime && existingSlot.to === endTime) ||
                    (newSlot.from === startTime && newSlot.to === endTime)
                ) {
                    continue;
                }

                if (newSlot.from < existingSlot.to && existingSlot.from < newSlot.to) {
                    throw new BadRequestException(`Overlap detected: New slot ${newSlot.from}-${newSlot.to} conflicts with existing slot ${existingSlot.from}-${existingSlot.to}`);
                }
            }
        }
        const mergedSlots: any[] = [];

        // Sort new slots by start time
        newSlots.sort((a, b) => a.from.localeCompare(b.from));

        // Process existing slots
        existingSlots.forEach(slot => {
            // Skip slots that exactly match start and end times
            if (slot.from === startTime && slot.to === endTime) {
                return;
            }

            let remainingSlot = { ...slot };

            // Process against each new slot
            for (const newSlot of newSlots) {
                // Slot completely before new slot
                if (remainingSlot.to <= newSlot.from) continue;

                // Slot completely after new slot
                if (remainingSlot.from >= newSlot.to) continue;

                // Partial overlaps - trim existing slot
                if (remainingSlot.from < newSlot.from) {
                    mergedSlots.push({
                        ...remainingSlot,
                        to: newSlot.from
                    });
                }

                if (remainingSlot.to > newSlot.to) {
                    remainingSlot = {
                        ...remainingSlot,
                        from: newSlot.to
                    };
                } else {
                    // Existing slot is completely consumed
                    remainingSlot = null;
                    break;
                }
            }

            // Add remaining portion of existing slot if any
            if (remainingSlot) {
                mergedSlots.push(remainingSlot);
            }
        });

        // Add new slots
        mergedSlots.push(...newSlots);

        // Sort the slots by start time
        mergedSlots.sort((a, b) => a.from.localeCompare(b.from));

        return mergedSlots;
    }

    private async handleSingleDateAvailability(staffAvailabilityDto: StaffAvailabilityDto, organizationId: string) {
        const date = new Date(staffAvailabilityDto.startDate);
        const dayOfWeek = date.toLocaleString("en-US", { weekday: "short" }).toLowerCase();
        const requestedSchedule = staffAvailabilityDto.schedule[dayOfWeek];
        const startOfDay = new Date(date.setHours(0, 0, 0, 0));
        const endOfDay = new Date(date.setHours(23, 59, 59, 999));
        if (staffAvailabilityDto.availabilityStatus === StaffAvailabilityEnum.AVAILABLE) {
            const facilityRecords = await this.FacilityAvailabilityModel.find({
                facilityId: new Types.ObjectId(staffAvailabilityDto.facilityId),
                organizationId: new Types.ObjectId(organizationId),
                $or: [
                    {
                        type: "unavailable",
                        fromDate: { $lte: endOfDay },
                        endDate: { $gte: startOfDay },
                    },
                    {
                        type: "available",
                    },
                ],
            });

            const unavailableRecord = facilityRecords.find((record) => record.type === "unavailable");
            const facilityAvailability = facilityRecords.find((record) => record.type === "available");

            if (unavailableRecord && requestedSchedule?.length > 0) {
                for (const requestedSlot of requestedSchedule) {
                    for (const unavailableSlot of unavailableRecord.time) {
                        if (this.isTimeOverlapping(requestedSlot, unavailableSlot)) {
                            throw new BadRequestException(`Facility is unavailable from ${unavailableSlot.from} to ${unavailableSlot.to} on ${startOfDay.toDateString()}`);
                        }
                    }
                }
            }

            if (!facilityAvailability) {
                throw new BadRequestException("No available working hours found for the facility.");
            }

            const facilityWorkingHours = facilityAvailability.workingHours;
            for (const day in staffAvailabilityDto.schedule) {
                const requestedSlots = staffAvailabilityDto.schedule[day];
                const availableSlots = facilityWorkingHours[day];

                if (!availableSlots && requestedSlots.length > 0) {
                    throw new BadRequestException(`Facility is not available on ${day}.`);
                }

                for (const requestedSlot of requestedSlots) {
                    const slotIsWithinAvailability = availableSlots.some((availableSlot: any) => this.isTimeWithinAvailableSlot(requestedSlot, availableSlot));

                    if (!slotIsWithinAvailability) {
                        const availableSlotsText = availableSlots.map((slot) => `${slot.from} to ${slot.to}`).join(", ");
                        throw new BadRequestException(
                            `Requested slot from ${requestedSlot.from} to ${requestedSlot.to} on ${day} is outside facility's available hours. (Working hours for this day are: ${availableSlotsText}).`,
                        );
                    }
                }
            }
        }
        const checkOverlapping = await this.staffAvailabilityModel.findOne({
            date: staffAvailabilityDto.startDate,
            userId: new Types.ObjectId(staffAvailabilityDto.userId),
            organizationId: new Types.ObjectId(organizationId),
            facilityId: new Types.ObjectId(staffAvailabilityDto.facilityId),
        });

        this.validateTimeSlots(staffAvailabilityDto.schedule);
        if (checkOverlapping && requestedSchedule?.length > 0) {
            for (const requestedSlot of requestedSchedule) {
                for (const existingSlot of checkOverlapping.timeSlots) {
                    if (this.isTimeOverlapping(requestedSlot, existingSlot)) {
                        throw new BadRequestException(`Unable to create slot: the requested time overlaps with an existing slot from ${existingSlot.from} to ${existingSlot.to}.`);
                    }
                }
            }
        }

        const transformedTimeSlots = this.transformTimeSlotsForSingle(
            staffAvailabilityDto.schedule,
            staffAvailabilityDto.availabilityStatus,
            staffAvailabilityDto.startDate,
            staffAvailabilityDto.classType,
            staffAvailabilityDto.privacy,
            staffAvailabilityDto.reason
        );
        if (checkOverlapping) {
            const updatedTimeSlots = this.mergeOrSplitTimeSlots(checkOverlapping.timeSlots, transformedTimeSlots);
            checkOverlapping.timeSlots = updatedTimeSlots;
            checkOverlapping["updatedAt"] = new Date();
            return await checkOverlapping.save();
        } else {
            const staffAvailabilitySingle = new this.staffAvailabilityModel({
                userId: staffAvailabilityDto.userId,
                facilityId: staffAvailabilityDto.facilityId,
                organizationId: organizationId,
                dateRange: staffAvailabilityDto.dateRange,
                date: staffAvailabilityDto.startDate,
                timeSlots: transformedTimeSlots,
            });

            return await staffAvailabilitySingle.save();
        }
    }

    private async createMultipleDateAvailability(staffAvailabilityDto: StaffAvailabilityDto, organizationId: string) {
        const session = await this.staffAvailabilityModel.startSession(); // Start a transaction session
        session.startTransaction();

        try {
            let { startDate, endDate, userId, facilityId, availabilityStatus, schedule, privacy, reason, classType, markType } = staffAvailabilityDto;
            if (markType === MarkAvailabilityType.CUSTOM) {
                if (!endDate) {
                    throw new BadRequestException("End date is required for custom date range");
                }

            }
            if (markType === MarkAvailabilityType.WEEKLY) {
                endDate = new Date(startDate);
                endDate.setFullYear(endDate.getFullYear() + 1);
            }

            if (startDate > endDate) {
                throw new BadRequestException("Start date should be less than end date");
            }
            if (staffAvailabilityDto.availabilityStatus === StaffAvailabilityEnum.AVAILABLE) {
                const Sdate = new Date(staffAvailabilityDto.startDate);
                const Edate = new Date(staffAvailabilityDto.endDate);
                const facilityRecords = await this.FacilityAvailabilityModel.find({
                    facilityId: new Types.ObjectId(staffAvailabilityDto.facilityId),
                    organizationId: new Types.ObjectId(organizationId),
                    $or: [
                        {
                            type: "unavailable",
                            fromDate: { $lte: Edate },
                            endDate: { $gte: Sdate },
                        },
                        {
                            type: "available",
                        },
                    ],
                });

                let unavailableRecord = facilityRecords.find((record) => record.type === "unavailable");
                const facilityAvailability = facilityRecords.find((record) => record.type === "available");
                if (unavailableRecord && staffAvailabilityDto.schedule) {
                    while (Sdate <= Edate) {
                        const dayOfWeek = Sdate.toLocaleDateString("en-US", { weekday: "short" }).toLowerCase();
                        unavailableRecord = facilityRecords.find((record) => record.fromDate <= Sdate && record.endDate >= Sdate);
                        const requestedSchedule = staffAvailabilityDto.schedule[dayOfWeek];
                        if (unavailableRecord && requestedSchedule?.length > 0) {
                            for (const requestedSlot of requestedSchedule) {
                                for (const unavailableSlot of unavailableRecord.time) {
                                    if (this.isTimeOverlapping(requestedSlot, unavailableSlot)) {
                                        throw new BadRequestException(`Facility is unavailable from ${unavailableSlot.from} to ${unavailableSlot.to} on ${Sdate.toDateString()}`);
                                    }
                                }
                            }
                        }
                        Sdate.setUTCDate(Sdate.getUTCDate() + 1);
                    }
                }

                if (!facilityAvailability) {
                    throw new BadRequestException("No available working hours found for the facility.");
                }

                const facilityWorkingHours = facilityAvailability.workingHours;
                for (const day in staffAvailabilityDto.schedule) {
                    const requestedSlots = staffAvailabilityDto.schedule[day];
                    const availableSlots = facilityWorkingHours[day];

                    if (!availableSlots && requestedSlots.length > 0) {
                        throw new BadRequestException(`Facility is not available on ${day}.`);
                    }

                    for (const requestedSlot of requestedSlots) {
                        const slotIsWithinAvailability = availableSlots.some((availableSlot: any) => this.isTimeWithinAvailableSlot(requestedSlot, availableSlot));

                        if (!slotIsWithinAvailability) {
                            const availableSlotsText = availableSlots.map((slot) => `${slot.from} to ${slot.to}`).join(", ");
                            throw new BadRequestException(
                                `Requested slot from ${requestedSlot.from} to ${requestedSlot.to} on ${day} is outside facility's available hours. (Working hours for this day are: ${availableSlotsText}).`,
                            );
                        }
                    }
                }
            }

            const overlappingSchedules = await this.staffAvailabilityModel
                .find({
                    date: { $gte: startDate, $lte: endDate },
                    userId: new Types.ObjectId(userId),
                    organizationId: new Types.ObjectId(organizationId),
                    facilityId: new Types.ObjectId(facilityId),
                })
                .sort({ date: 1 })
                .session(session);

            const existingDatesData = overlappingSchedules.reduce((acc, schedule) => {
                acc[schedule._id] = schedule.date.toISOString();
                return acc;
            }, {} as Record<string, string>);

            if (overlappingSchedules.length > 0) {
                for (const singleAvailability of overlappingSchedules) {
                    const dayOfWeek = singleAvailability.date
                        .toLocaleDateString("en-US", { weekday: "short" })
                        .toLowerCase();

                    if (singleAvailability.timeSlots.length > 0 && schedule[dayOfWeek]?.length > 0) {
                        for (const requestedSlot of schedule[dayOfWeek]) {
                            for (const existingSlot of singleAvailability.timeSlots) {
                                if (this.isTimeOverlapping(requestedSlot, existingSlot)) {
                                    throw new BadRequestException(
                                        `Unable to create slot: On ${singleAvailability.date.toDateString()}, the requested time overlaps with an existing slot from ${existingSlot.from} to ${existingSlot.to}.`
                                    );
                                }
                            }
                        }
                    }
                }
            }

            this.validateTimeSlots(schedule);

            const insertPromises = [];
            let currentDate = new Date(startDate);
            const end = new Date(endDate);

            while (currentDate <= end) {
                const dayOfWeek = currentDate
                    .toLocaleDateString("en-US", { weekday: "short" })
                    .toLowerCase();

                if (schedule[dayOfWeek]) {
                    const currentDayTimeSlots = this.transformTimeSlotsForMultiple(
                        schedule[dayOfWeek],
                        availabilityStatus,
                        classType,
                        privacy,
                        reason,
                    );

                    if (currentDayTimeSlots?.length > 0) {
                        const currentDateISO = currentDate.toISOString();
                        const existingId = Object.keys(existingDatesData).find(
                            key => existingDatesData[key] === currentDateISO,
                        );

                        if (existingId) {
                            const existingSchedule = overlappingSchedules.find(
                                sch => sch._id.toString() === existingId.toString(),
                            );

                            if (existingSchedule) {
                                const updatedTimeSlots = this.mergeOrSplitTimeSlots(
                                    existingSchedule.timeSlots,
                                    currentDayTimeSlots,
                                );
                                existingSchedule.timeSlots = updatedTimeSlots;
                                existingSchedule["updatedAt"] = new Date();

                                insertPromises.push(existingSchedule.save({ session }));
                            }
                        } else {
                            const staffAvailability = new this.staffAvailabilityModel({
                                userId: new Types.ObjectId(userId),
                                facilityId: new Types.ObjectId(facilityId),
                                organizationId: new Types.ObjectId(organizationId),
                                dateRange: DateRange.MULTIPLE,
                                date: new Date(currentDate),
                                timeSlots: currentDayTimeSlots,
                            });
                            insertPromises.push(staffAvailability.save({ session }));
                        }
                    }
                }

                currentDate.setUTCDate(currentDate.getUTCDate() + 1);
            }

            await Promise.all(insertPromises);

            await session.commitTransaction();
            return "Multiple date availability created successfully";
        } catch (error) {
            await session.abortTransaction();
            console.error("Error creating multiple date availability:", error);
            throw error instanceof BadRequestException
                ? error
                : new InternalServerErrorException(
                    "An error occurred while creating multiple date availability. Please try again later.",
                );
        } finally {
            session.endSession();
        }
    }

    async createStaffAvailability(staffAvailabilityDto: StaffAvailabilityDto, user: any): Promise<any> {
        let query = { facilityId: { $in: [new Types.ObjectId(staffAvailabilityDto.facilityId)] } };

        if (user.role.type == ENUM_ROLE_TYPE.ORGANIZATION) {
            query["organizationId"] = new Types.ObjectId(user._id);
        }
        if (user.role.type == ENUM_ROLE_TYPE.WEB_MASTER || user.role.type == ENUM_ROLE_TYPE.FRONT_DESK_ADMIN) {
            query["userId"] = new Types.ObjectId(user._id);
        }

        const facilityDetails = await this.StaffModel.findOne(query).select({ organizationId: 1 });
        if (!facilityDetails) {
            throw new BadRequestException("Invalid facilityId");
        }

        let organizationId = facilityDetails?.organizationId;

        switch (staffAvailabilityDto.dateRange) {
            case DateRange.SINGLE:
                await this.handleSingleDateAvailability(staffAvailabilityDto, organizationId);
                break;
            case DateRange.MULTIPLE:
                await this.createMultipleDateAvailability(staffAvailabilityDto, organizationId);
                break;
            default:
                throw new BadRequestException("Please provide a correct date range");
        }
    }

    async listStaffAvailability(listStaffAvailabilityDto: ListStaffAvailabilityDto, organizationId: IDatabaseObjectId, user: any): Promise<any> {
        const { userIds, facilityIds, startDate, endDate, classType, payRateIds } = listStaffAvailabilityDto;

        // Build the filter with date range and optional user/facility filters
        const filter: any = {
            organizationId: new Types.ObjectId(organizationId),
            $and: [{ date: { $lte: new Date(endDate) } }, { date: { $gte: new Date(startDate) } }],
        };

        if (userIds?.length > 0) {
            filter.userId = { $in: userIds.map((e) => new Types.ObjectId(e)) };
        }
        // if (user.role.type === ENUM_ROLE_TYPE.ORGANIZATION) {
        //     filter.organizationId = new Types.ObjectId(user._id);
        // }

        // if (user.role.type === ENUM_ROLE_TYPE.TRAINER) {
        //     filter.userId = new Types.ObjectId(user._id);
        // }

        if (facilityIds && facilityIds.length > 0) {
            filter.facilityId = { $in: facilityIds.map((e) => new Types.ObjectId(e)) };
        }

        if (payRateIds?.length > 0) {
            filter["timeSlots.payRateIds"] = { $in: payRateIds.map((id) => new Types.ObjectId(id)) };
        }

        if (classType) {
            filter["timeSlots.classType"] = classType;
        }

        // Aggregate staff availability data
        const staffAvailabilities = await this.staffAvailabilityModel.aggregate([
            { $match: filter },
            {
                $lookup: {
                    from: "users",
                    localField: "userId",
                    foreignField: "_id",
                    as: "user",
                },
            },
            {
                $unwind: {
                    path: "$user",
                    preserveNullAndEmptyArrays: true,
                },
            },
            {
                $addFields: {
                    userName: { $concat: ["$user.firstName", " ", "$user.lastName"] },
                },
            },
            {
                $project: {
                    _id: 1,
                    userId: 1,
                    organizationId: 1,
                    facilityId: 1,
                    date: 1,
                    timeSlots: {
                        $filter: {
                            input: "$timeSlots",
                            as: "slot",
                            cond: {
                                $and: [
                                    classType
                                        ? { $eq: ["$$slot.classType", classType] }
                                        : { $ne: ["$$slot.classType", null] },
                                    payRateIds?.length > 0
                                        ? {
                                            $setIsSubset: [payRateIds.map((id) => new Types.ObjectId(id)), "$$slot.payRateIds"],
                                        }
                                        : { $ne: ["$$slot.payRateIds", null] },
                                ],
                            },
                        },
                    },
                    userName: 1,
                },
            },
            {
                $group: {
                    _id: {
                        userId: "$userId",
                        facilityId: "$facilityId",
                        organizationId: "$organizationId",
                    },
                    userName: { $first: "$userName" },
                    schedule: {
                        $push: {
                            dateId: "$_id",
                            date: "$date",
                            timeSlots: "$timeSlots",
                        },
                    },
                },
            },
            {
                $project: {
                    _id: 1,
                    userName: 1,
                    schedule: {
                        $filter: {
                            input: "$schedule",
                            as: "day",
                            cond: { $gt: [{ $size: "$$day.timeSlots" }, 0] }, // Only include objects with non-empty timeSlots
                        },
                    },
                    startDate: new Date(startDate),
                    endDate: new Date(endDate),
                },
            },
        ]);

        return staffAvailabilities;
    }

    async listStaffAvailabilityListByTypes(listStaffAvailabilityDto: ListStaffAvailabilityByTypesDto, user: any): Promise<any> {
        const { userIds, facilityIds, startDate, endDate, classType, serviceCategoryId, subTypeId } = listStaffAvailabilityDto;

        // Build the filter with date range and optional user/facility filters
        const filter: any = {
            date: {
                $gte: new Date(new Date(startDate).setUTCHours(0, 0, 0, 0)),
                $lte: new Date(new Date(endDate).setUTCHours(23, 59, 59, 999)),
            },
        };

        if (userIds?.length > 0) {
            filter.userId = { $in: userIds.map((e) => new Types.ObjectId(e)) };
        }
        if (user.role.type === ENUM_ROLE_TYPE.ORGANIZATION) {
            filter.organizationId = new Types.ObjectId(user._id);
        }

        if (user.role.type === ENUM_ROLE_TYPE.TRAINER) {
            filter.userId = new Types.ObjectId(user._id);
            filter.facilityId = { $in: facilityIds.map((e) => new Types.ObjectId(e)) };
        }

        if (user.role.type === ENUM_ROLE_TYPE.ORGANIZATION || user.role.type === ENUM_ROLE_TYPE.WEB_MASTER || user.role.type === ENUM_ROLE_TYPE.FRONT_DESK_ADMIN) {
            if (facilityIds?.length > 0) {
                filter.facilityId = { $in: facilityIds.map((e) => new Types.ObjectId(e)) };
            }
        }

        if (classType) {
            filter["timeSlots.classType"] = classType;
        }

        if (facilityIds) {
            filter.facilityId = { $in: facilityIds.map((e) => new Types.ObjectId(e)) };
        }

        // Aggregate staff availability data
        const pipeline: PipelineStage[] = [
            {
                $match: filter
            },
            {
                $lookup: {
                    from: "users",
                    localField: "userId",
                    foreignField: "_id",
                    as: "user"
                }
            },
            {
                $unwind: {
                    path: "$user",
                    preserveNullAndEmptyArrays: true
                }
            },
            {
                $unwind: {
                    path: "$timeSlots",
                    preserveNullAndEmptyArrays: true
                }
            },
            {
                $unwind: {
                    path: "$timeSlots.payRateIds",
                    preserveNullAndEmptyArrays: true
                }
            },
            {
                $lookup: {
                    from: "payrates",
                    localField: "timeSlots.payRateIds",
                    foreignField: "_id",
                    as: "payrate",
                    pipeline: [
                        {
                            $match: {
                                serviceCategory: new Types.ObjectId(serviceCategoryId),
                                appointmentType: new Types.ObjectId(subTypeId),
                                serviceType: classType
                            }
                        },
                        {
                            $project: {
                                _id: 1
                            }
                        }
                    ]
                }
            },
            {
                $match: {
                    payrate: { $ne: [] }
                }
            },
            {
                $addFields: {
                    userName: {
                        $concat: [
                            "$user.firstName",
                            " ",
                            "$user.lastName"
                        ]
                    },
                    "timeSlots.payRateIds": [
                        "$timeSlots.payRateIds"
                    ]
                }
            },
            {
                $group: {
                    _id: {
                        userId: "$userId",
                        facilityId: "$facilityId",
                        organizationId: "$organizationId",
                        date: "$date"
                    },
                    userName: { $first: "$userName" },
                    date: {
                        $first: "$date"
                    },
                    dateId: {
                        $first: "$_id"
                    },
                    timeSlots: {
                        $push: "$timeSlots"
                    }
                }
            },
            {
                $group: {
                    _id: {
                        userId: "$_id.userId",
                        facilityId: "$_id.facilityId",
                        organizationId: "$_id.organizationId"
                    },
                    userName: { $first: "$userName" },
                    schedule: {
                        $push: {
                            dateId: "$dateId",
                            date: "$date",
                            timeSlots: "$timeSlots"
                        }
                    },
                }
            },
            {
                $set: {
                    startDate: startDate,
                    endDate: endDate
                }
            }
        ]

        const staffAvailabilities = await this.staffAvailabilityModel.aggregate(pipeline);

        return staffAvailabilities;
    }

    async listStaffAvailabilityListByTypesForApp(listStaffAvailabilityDto: ListStaffAvailabilityByTypesDto): Promise<any> {
        const { userIds, facilityIds, startDate, endDate, classType, serviceCategoryId, subTypeId } = listStaffAvailabilityDto;

        // Build the filter with date range and optional user/facility filters
        const filter: any = {
            date: {
                $gte: new Date(new Date(startDate).setUTCHours(0, 0, 0, 0)),
                $lte: new Date(new Date(endDate).setUTCHours(23, 59, 59, 999)),
            },
        };

        if (userIds?.length > 0) {
            filter.userId = { $in: userIds.map((e) => new Types.ObjectId(e)) };
        }

        if (classType) {
            filter["timeSlots.classType"] = classType;
        }

        if (!facilityIds) {
            throw new Error('facilityIds is required');
        }
        filter.facilityId = { $in: facilityIds.map((e) => new Types.ObjectId(e)) };


        // Aggregate staff availability data
        const pipeline: PipelineStage[] = [
            {
                $match: filter
            },
            {
                $lookup: {
                    from: "users",
                    localField: "userId",
                    foreignField: "_id",
                    as: "user"
                }
            },
            {
                $unwind: {
                    path: "$user",
                    preserveNullAndEmptyArrays: true
                }
            },
            {
                $unwind: {
                    path: "$timeSlots",
                    preserveNullAndEmptyArrays: true
                }
            },
            {
                $unwind: {
                    path: "$timeSlots.payRateIds",
                    preserveNullAndEmptyArrays: true
                }
            },
            {
                $lookup: {
                    from: "payrates",
                    localField: "timeSlots.payRateIds",
                    foreignField: "_id",
                    as: "payrate",
                    pipeline: [
                        {
                            $match: {
                                serviceCategory: new Types.ObjectId(serviceCategoryId),
                                appointmentType: new Types.ObjectId(subTypeId),
                                serviceType: classType
                            }
                        },
                        {
                            $project: {
                                _id: 1
                            }
                        }
                    ]
                }
            },
            {
                $match: {
                    payrate: { $ne: [] }
                }
            },
            {
                $addFields: {
                    userName: {
                        $concat: [
                            "$user.firstName",
                            " ",
                            "$user.lastName"
                        ]
                    },
                    "timeSlots.payRateIds": [
                        "$timeSlots.payRateIds"
                    ]
                }
            },
            {
                $group: {
                    _id: {
                        userId: "$userId",
                        facilityId: "$facilityId",
                        organizationId: "$organizationId",
                        date: "$date"
                    },
                    userName: { $first: "$userName" },
                    date: {
                        $first: "$date"
                    },
                    dateId: {
                        $first: "$_id"
                    },
                    timeSlots: {
                        $push: "$timeSlots"
                    }
                }
            },
            {
                $group: {
                    _id: {
                        userId: "$_id.userId",
                        facilityId: "$_id.facilityId",
                        organizationId: "$_id.organizationId"
                    },
                    userName: { $first: "$userName" },
                    schedule: {
                        $push: {
                            dateId: "$dateId",
                            date: "$date",
                            timeSlots: "$timeSlots"
                        }
                    },
                }
            },
            {
                $set: {
                    startDate: startDate,
                    endDate: endDate
                }
            }
        ]

        const staffAvailabilities = await this.staffAvailabilityModel.aggregate(pipeline);

        return staffAvailabilities;
    }

    async staffAvailabilityTypes(dto: StaffAvailabilityByTypeDto, user: any): Promise<any> {
        const { userId, facilityId, date, classType, serviceCategory, subTypeId } = dto;

        // Build the filter with date range and optional user/facility filters
        const filter: any = {
            $and: [
                { date: { $lte: new Date(new Date(date).setUTCHours(23, 59, 59, 999)) } },
                { date: { $gte: new Date(new Date(date).setUTCHours(0, 0, 0, 0)) } }
            ],
        };

        if (userId) {
            filter.userId = new Types.ObjectId(userId);
        }
        if (user.role.type === ENUM_ROLE_TYPE.ORGANIZATION) {
            filter.organizationId = new Types.ObjectId(user._id);
        }

        if (user.role.type === ENUM_ROLE_TYPE.TRAINER) {
            filter.userId = new Types.ObjectId(user._id);
            filter.facilityId = new Types.ObjectId(facilityId);
        }

        if (user.role.type === ENUM_ROLE_TYPE.ORGANIZATION || user.role.type === ENUM_ROLE_TYPE.WEB_MASTER || user.role.type === ENUM_ROLE_TYPE.FRONT_DESK_ADMIN) {
            filter.facilityId = new Types.ObjectId(facilityId);
        }

        if (classType) {
            filter["timeSlots.classType"] = classType;
        }

        if (facilityId) {
            filter.facilityId = new Types.ObjectId(facilityId);
        }

        // Simplified and optimized aggregation pipeline
        const pipeline = [
            { $match: filter },
            {
                $lookup: {
                    from: "users",
                    localField: "userId",
                    foreignField: "_id",
                    as: "user",
                    pipeline: [
                        {
                            $project: {
                                firstName: 1,
                                lastName: 1
                            }
                        }
                    ]
                }
            },
            {
                $unwind: {
                    path: "$user",
                    preserveNullAndEmptyArrays: true,
                }
            },
            {
                $lookup: {
                    from: "staffprofiledetails",
                    localField: "userId",
                    foreignField: "userId",
                    as: "staff",
                    pipeline: [
                        {
                            $project: {
                                profilePicture: 1,
                                gender: 1,
                                _id: 0
                            }
                        }
                    ]
                }
            },
            {
                $unwind: {
                    path: "$staff",
                    preserveNullAndEmptyArrays: true
                }
            },
            {
                $project: {
                    _id: 1,
                    userId: 1,
                    organizationId: 1,
                    facilityId: 1,
                    date: 1,
                    timeSlots: {
                        $filter: {
                            input: "$timeSlots",
                            as: "slot",
                            cond: { $ne: ["$$slot.availabilityStatus", "unavailable"] }
                        }
                    },
                    userName: { $concat: ["$user.firstName", " ", "$user.lastName"] },
                    staff: 1,
                }
            },
            {
                $match: {
                    "timeSlots": { $ne: [] }
                }
            },
            {
                $group: {
                    _id: {
                        userId: "$userId",
                        facilityId: "$facilityId",
                        organizationId: "$organizationId",
                    },
                    userName: { $first: "$userName" },
                    staffDetail: { $first: "$staff" },
                    schedule: {
                        $push: {
                            dateId: "$_id",
                            date: "$date",
                            timeSlots: "$timeSlots"
                        }
                    }
                }
            }
        ];

        const staffAvailabilities = await this.staffAvailabilityModel.aggregate(pipeline);
        const dayOfWeek = moment(date).tz("Asia/Kolkata").format("ddd").toLowerCase();
        const startOfDay = moment(date).tz("Asia/Kolkata").startOf("day").toDate();
        const endOfDay = moment(date).tz("Asia/Kolkata").endOf("day").toDate();
        const currentISTTime = moment().tz("Asia/Kolkata").format("HH:mm");



        const facilityRecords = await this.FacilityAvailabilityModel.find({
            facilityId: new Types.ObjectId(facilityId),
            $or: [
                { type: "unavailable", fromDate: { $lte: endOfDay }, endDate: { $gte: startOfDay } },
                { type: "available" },
            ],
        }).lean();

        const unavailableRecord = facilityRecords.find(r => r.type === "unavailable");
        const facilityAvailability = facilityRecords.find(r => r.type === "available");
        const facilityWorkingHours = facilityAvailability?.workingHours?.[dayOfWeek] || [];
        const facilityNotAvailableHours = unavailableRecord?.time || [];
        let roomavAvailable: any

        const bookedRooms = await this.roomModel.aggregate([
            {
                $match: {
                    facilityId: new Types.ObjectId(facilityId),
                    classType,
                    serviceCategory: new Types.ObjectId(serviceCategory),
                    status: true
                }
            },
            {
                $lookup: {
                    from: "schedulings",
                    let: { roomId: "$_id" },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $and: [
                                        { $eq: ["$roomId", "$$roomId"] },
                                        { $eq: ["$facilityId", new Types.ObjectId(facilityId)] },
                                        { $in: ["$scheduleStatus", [ScheduleStatusType.BOOKED, ScheduleStatusType.CHECKEDIN]] },
                                        { $gte: ["$date", startOfDay] },
                                        { $lte: ["$date", endOfDay] },
                                        { $gt: ["$to", currentISTTime] }
                                    ]
                                }
                            }
                        }
                    ],
                    as: "bookings"
                }
            },
            {
                $addFields: { bookingsCount: { $size: "$bookings" } }
            },
            {
                $project: {
                    _id: 1,
                    roomId: "$_id",
                    roomName: 1,
                    capacity: 1,
                    bookingsCount: 1,
                    bookings: {
                        $map: {
                            input: "$bookings",
                            as: "b",
                            in: { from: "$$b.from", to: "$$b.to" }
                        }
                    }
                }
            }
        ]);
        const fullyBookedRoomIds = bookedRooms
            .filter(room => room.bookingsCount >= room.capacity)
            .map(room => room._id);
        const availableRooms = await this.roomModel.aggregate([
            {
                $match: {
                    facilityId: new Types.ObjectId(facilityId),
                    _id: { $nin: fullyBookedRoomIds },
                    classType,
                    serviceCategory: new Types.ObjectId(serviceCategory),
                    status: true
                }
            },
            {
                $project: {
                    _id: 1,
                    roomId: "$_id",
                    roomName: 1,
                    capacity: 1,
                    isavailable: { $literal: true }
                }
            }
        ]);
        if (availableRooms.length > 0) {

            roomavAvailable = this.calculateAvailableHours(facilityWorkingHours, facilityNotAvailableHours);
        }

        else if (bookedRooms.length > 0) {
            const roomNotAvailHours = bookedRooms.flatMap(room => room.bookings);

            roomavAvailable = this.calculateAvailableHours(facilityWorkingHours, roomNotAvailHours);
        }
        else {

            roomavAvailable = [];
        }
        // Extract unique payRateIds more efficiently
        const payrateIds = [...new Set(
            staffAvailabilities.flatMap(item =>
                item.schedule.flatMap(day =>
                    day.timeSlots?.flatMap(slot => slot.payRateIds || []) || []
                )
            )
        )];

        // Fetch payrate details in parallel with service types
        const [payrateDetails, serviceTypes, schedules] = await Promise.all([
            this.PayRateModel.find({
                _id: { $in: payrateIds },
                serviceCategory,
                appointmentType: subTypeId,
                userId: new Types.ObjectId(userId)
            }),
            this.ServiceModel.find({
                _id: serviceCategory,
                'appointmentType._id': subTypeId
            }, {
                'appointmentType.$': 1
            }).sort({ updatedAt: -1 }),
            this.SchedulingModel.find({
                trainerId: new Types.ObjectId(userId),
                date: {
                    $gte: new Date(date.setHours(0, 0, 0, 0)),
                    $lte: new Date(date.setHours(23, 59, 59, 999))
                },
                scheduleStatus: { $ne: ScheduleStatusType.CANCELED }
            }, {
                from: 1,
                to: 1,
                date: 1
            })
        ]);

        // Process availabilities with optimized slot breaking
        const availabilityList = await this.processAvailabilities(
            staffAvailabilities,
            payrateDetails,
            serviceTypes,
            serviceCategory,
            subTypeId,
            schedules
        );
        if (!availabilityList.length) {
            return {
                userId: staffAvailabilities[0]?._id?.userId || null,
                gender: staffAvailabilities[0]?.staffDetail?.gender,
                profilePicture: staffAvailabilities[0]?.staffDetail?.profilePicture,
                organizationId: staffAvailabilities[0]?._id?.organizationId || null,
                facilityId: staffAvailabilities[0]?._id?.facilityId || null,
                userName: staffAvailabilities[0]?.userName || null,
                dateId: staffAvailabilities[0]?.schedule[0]?.dateId || null,
                date: staffAvailabilities[0]?.schedule[0]?.date || null,
                schedule: [],

            }
        }
        if (!roomavAvailable.length) {
            return {
                userId: staffAvailabilities[0]?._id?.userId || null,
                gender: staffAvailabilities[0]?.staffDetail?.gender,
                profilePicture: staffAvailabilities[0]?.staffDetail?.profilePicture,
                organizationId: staffAvailabilities[0]?._id?.organizationId || null,
                facilityId: staffAvailabilities[0]?._id?.facilityId || null,
                userName: staffAvailabilities[0]?.userName || null,
                dateId: staffAvailabilities[0]?.schedule[0]?.dateId || null,
                date: staffAvailabilities[0]?.schedule[0]?.date || null,
                schedule: [],

            }
        }
        const filtered = this.filterScheduleByTimeRange(availabilityList, roomavAvailable);
        if (filtered.length === 0) {
            return {
                userId: staffAvailabilities[0]?._id?.userId || null,
                gender: staffAvailabilities[0]?.staffDetail?.gender,
                profilePicture: staffAvailabilities[0]?.staffDetail?.profilePicture,
                organizationId: staffAvailabilities[0]?._id?.organizationId || null,
                facilityId: staffAvailabilities[0]?._id?.facilityId || null,
                userName: staffAvailabilities[0]?.userName || null,
                dateId: staffAvailabilities[0]?.schedule[0]?.dateId || null,
                date: staffAvailabilities[0]?.schedule[0]?.date || null,
                schedule: [],

            }
        }
        else {
            return {
                userId: filtered[0]?._id?.userId || null,
                gender: staffAvailabilities[0]?.staffDetail?.gender,
                profilePicture: staffAvailabilities[0]?.staffDetail?.profilePicture,
                organizationId: filtered[0]?._id?.organizationId || null,
                facilityId: filtered[0]?._id?.facilityId || null,
                userName: filtered[0]?.userName || null,
                dateId: filtered[0]?.schedule[0]?.dateId || null,
                date: filtered[0]?.schedule[0]?.date || null,
                schedule: roomavAvailable.length > 0 ? filtered[0]?.schedule : [],

            }
        }
    }

    async staffAvailabilityTypesForApp(dto: StaffAvailabilityByTypeDto): Promise<any> {
        const { userId, facilityId, timeSlotId, date, classType, serviceCategory, subTypeId } = dto;

        const filter: any = {
            $and: [
                { date: { $lte: new Date(new Date(date).setUTCHours(23, 59, 59, 999)) } },
                { date: { $gte: new Date(new Date(date).setUTCHours(0, 0, 0, 0)) } }
            ],
        };
        if (classType) {
            filter["timeSlots.classType"] = classType;
        }
        if (!facilityId) {
            throw new Error('facilityId is required');
        }

        if (timeSlotId) {
            filter["timeSlots._id"] = new Types.ObjectId(timeSlotId);
        }

        filter.facilityId = new Types.ObjectId(facilityId);

        // Simplified and optimized aggregation pipeline
        const pipeline = [
            { $match: filter },
            {
                $lookup: {
                    from: "users",
                    localField: "userId",
                    foreignField: "_id",
                    as: "user",
                    pipeline: [
                        {
                            $project: {
                                firstName: 1,
                                lastName: 1
                            }
                        }
                    ]
                }
            },
            {
                $unwind: {
                    path: "$user",
                    preserveNullAndEmptyArrays: true,
                }
            },
            {
                $lookup: {
                    from: "staffprofiledetails",
                    localField: "userId",
                    foreignField: "userId",
                    as: "staff",
                    pipeline: [
                        {
                            $project: {
                                profilePicture: 1,
                                gender: 1,
                                _id: 0
                            }
                        }
                    ]
                }
            },
            {
                $unwind: {
                    path: "$staff",
                    preserveNullAndEmptyArrays: true
                }
            },
            {
                $project: {
                    _id: 1,
                    userId: 1,
                    organizationId: 1,
                    facilityId: 1,
                    date: 1,
                    timeSlots: {
                        $filter: {
                            input: "$timeSlots",
                            as: "slot",
                            cond: {
                                $and: [
                                    { $ne: ["$$slot.availabilityStatus", "unavailable"] },
                                    timeSlotId ? { $eq: ["$$slot._id", new Types.ObjectId(timeSlotId)] } : true
                                ]
                            }
                            // cond: { $ne: ["$$slot.availabilityStatus", "unavailable"] }
                        }
                    },
                    userName: { $concat: ["$user.firstName", " ", "$user.lastName"] },
                    staff: 1,
                }
            },
            {
                $match: {
                    "timeSlots": { $ne: [] }
                }
            },
            {
                $group: {
                    _id: {
                        userId: "$userId",
                        facilityId: "$facilityId",
                        organizationId: "$organizationId",
                    },
                    userName: { $first: "$userName" },
                    staffDetail: { $first: "$staff" },
                    schedule: {
                        $push: {
                            dateId: "$_id",
                            date: "$date",
                            timeSlots: "$timeSlots"
                        }
                    }
                }
            }
        ];

        const staffAvailabilities = await this.staffAvailabilityModel.aggregate(pipeline);
        const dayOfWeek = moment(date).tz("Asia/Kolkata").format("ddd").toLowerCase();
        const startOfDay = moment(date).tz("Asia/Kolkata").startOf("day").toDate();
        const endOfDay = moment(date).tz("Asia/Kolkata").endOf("day").toDate();
        const currentISTTime = moment().tz("Asia/Kolkata").format("HH:mm");



        const facilityRecords = await this.FacilityAvailabilityModel.find({
            facilityId: new Types.ObjectId(facilityId),
            $or: [
                { type: "unavailable", fromDate: { $lte: endOfDay }, endDate: { $gte: startOfDay } },
                { type: "available" },
            ],
        }).lean();

        const unavailableRecord = facilityRecords.find(r => r.type === "unavailable");
        const facilityAvailability = facilityRecords.find(r => r.type === "available");
        const facilityWorkingHours = facilityAvailability?.workingHours?.[dayOfWeek] || [];
        const facilityNotAvailableHours = unavailableRecord?.time || [];
        let roomavAvailable: any

        const bookedRooms = await this.roomModel.aggregate([
            {
                $match: {
                    facilityId: new Types.ObjectId(facilityId),
                    classType,
                    serviceCategory: new Types.ObjectId(serviceCategory),
                    status: true
                }
            },
            {
                $lookup: {
                    from: "schedulings",
                    let: { roomId: "$_id" },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $and: [
                                        { $eq: ["$roomId", "$$roomId"] },
                                        { $eq: ["$facilityId", new Types.ObjectId(facilityId)] },
                                        { $in: ["$scheduleStatus", [ScheduleStatusType.BOOKED, ScheduleStatusType.CHECKEDIN]] },
                                        { $gte: ["$date", startOfDay] },
                                        { $lte: ["$date", endOfDay] },
                                        { $gt: ["$to", currentISTTime] }
                                    ]
                                }
                            }
                        }
                    ],
                    as: "bookings"
                }
            },
            {
                $addFields: { bookingsCount: { $size: "$bookings" } }
            },
            {
                $project: {
                    _id: 1,
                    roomId: "$_id",
                    roomName: 1,
                    capacity: 1,
                    bookingsCount: 1,
                    bookings: {
                        $map: {
                            input: "$bookings",
                            as: "b",
                            in: { from: "$$b.from", to: "$$b.to" }
                        }
                    }
                }
            }
        ]);
        const fullyBookedRoomIds = bookedRooms
            .filter(room => room.bookingsCount >= room.capacity)
            .map(room => room._id);
        const availableRooms = await this.roomModel.aggregate([
            {
                $match: {
                    facilityId: new Types.ObjectId(facilityId),
                    _id: { $nin: fullyBookedRoomIds },
                    classType,
                    serviceCategory: new Types.ObjectId(serviceCategory),
                    status: true
                }
            },
            {
                $project: {
                    _id: 1,
                    roomId: "$_id",
                    roomName: 1,
                    capacity: 1,
                    isavailable: { $literal: true }
                }
            }
        ]);
        let facilityAvailableHours = this.calculateAvailableHours(facilityWorkingHours, facilityNotAvailableHours);

        if (availableRooms?.length > 0) {
            roomavAvailable = facilityAvailableHours;

        }

        else if (bookedRooms?.length > 0) {
            const roomNotAvailHours = bookedRooms.flatMap(room => room.bookings);
            roomavAvailable = this.calculateAvailableHoursForBookedRooms(facilityAvailableHours, roomNotAvailHours);
        }
        else {

            roomavAvailable = [];
        }
        const payrateIds = [...new Set(
            staffAvailabilities.flatMap(item =>
                item.schedule.flatMap(day =>
                    day.timeSlots?.flatMap(slot => slot.payRateIds || []) || []
                )
            )
        )];

        const [payrateDetails, serviceTypes, schedules] = await Promise.all([
            this.PayRateModel.find({
                _id: { $in: payrateIds },
                serviceCategory,
                appointmentType: subTypeId,
                userId: new Types.ObjectId(userId)

            }),
            this.ServiceModel.find({
                _id: serviceCategory,
                'appointmentType._id': subTypeId
            }, {
                'appointmentType.$': 1
            }).sort({ updatedAt: -1 }),

            this.SchedulingModel.find({
                trainerId: new Types.ObjectId(userId),
                date: {
                    $gte: new Date(date.setHours(0, 0, 0, 0)),
                    $lte: new Date(date.setHours(23, 59, 59, 999))
                },
                scheduleStatus: { $ne: ScheduleStatusType.CANCELED }
            }, {
                from: 1,
                to: 1,
                date: 1
            })
        ]);

        const availabilityList = await this.processAvailabilities(
            staffAvailabilities,
            payrateDetails,
            serviceTypes,
            serviceCategory,
            subTypeId,
            schedules
        );
        if (!availabilityList.length) {
            return {
                userId: staffAvailabilities[0]?._id?.userId || null,
                gender: staffAvailabilities[0]?.staffDetail?.gender,
                profilePicture: staffAvailabilities[0]?.staffDetail?.profilePicture,
                organizationId: staffAvailabilities[0]?._id?.organizationId || null,
                facilityId: staffAvailabilities[0]?._id?.facilityId || null,
                userName: staffAvailabilities[0]?.userName || null,
                dateId: staffAvailabilities[0]?.schedule[0]?.dateId || null,
                date: staffAvailabilities[0]?.schedule[0]?.date || null,
                schedule: [],

            }
        }
        if (!roomavAvailable.length) {
            return {
                userId: staffAvailabilities[0]?._id?.userId || null,
                gender: staffAvailabilities[0]?.staffDetail?.gender,
                profilePicture: staffAvailabilities[0]?.staffDetail?.profilePicture,
                organizationId: staffAvailabilities[0]?._id?.organizationId || null,
                facilityId: staffAvailabilities[0]?._id?.facilityId || null,
                userName: staffAvailabilities[0]?.userName || null,
                dateId: staffAvailabilities[0]?.schedule[0]?.dateId || null,
                date: staffAvailabilities[0]?.schedule[0]?.date || null,
                schedule: [],

            }
        }
        const filtered = this.filterScheduleByTimeRange(availabilityList, roomavAvailable);
        if (filtered.length === 0) {
            return {
                userId: staffAvailabilities[0]?._id?.userId || null,
                gender: staffAvailabilities[0]?.staffDetail?.gender,
                profilePicture: staffAvailabilities[0]?.staffDetail?.profilePicture,
                organizationId: staffAvailabilities[0]?._id?.organizationId || null,
                facilityId: staffAvailabilities[0]?._id?.facilityId || null,
                userName: staffAvailabilities[0]?.userName || null,
                dateId: staffAvailabilities[0]?.schedule[0]?.dateId || null,
                date: staffAvailabilities[0]?.schedule[0]?.date || null,
                schedule: [],

            }
        }
        else {
            return {
                userId: filtered[0]?._id?.userId || null,
                gender: staffAvailabilities[0]?.staffDetail?.gender,
                profilePicture: staffAvailabilities[0]?.staffDetail?.profilePicture,
                organizationId: filtered[0]?._id?.organizationId || null,
                facilityId: filtered[0]?._id?.facilityId || null,
                userName: filtered[0]?.userName || null,
                dateId: filtered[0]?.schedule[0]?.dateId || null,
                date: filtered[0]?.schedule[0]?.date || null,
                schedule: roomavAvailable.length > 0 ? filtered[0]?.schedule : [],

            }
        }
    }


    private async processAvailabilities(
        staffAvailabilities: any[],
        payrateDetails: any[],
        serviceTypes: any[],
        serviceCategory: string,
        subTypeId: string,
        schedules: any[]
    ): Promise<any[]> {
        const availabilityList: any[] = [];

        for (const item of staffAvailabilities) {
            const userId = item._id.userId;
            const selectedPayrate = payrateDetails.find(p =>
                p.userId.toString() === userId.toString()
            );

            if (!selectedPayrate) continue;

            const serviceType = serviceTypes.find(s =>
                s._id.toString() === serviceCategory.toString() &&
                s.appointmentType[0]._id.toString() === subTypeId.toString()
            );

            if (!serviceType) continue;

            const durationInMinutes = serviceType.appointmentType[0].durationInMinutes;

            for (const day of item.schedule) {
                const processedTimeSlots = [];

                for (const slot of day.timeSlots || []) {
                    const slotStartTime = moment(slot.from, 'HH:mm');
                    const slotEndTime = moment(slot.to, 'HH:mm');

                    while (slotStartTime.add(durationInMinutes, 'minutes').isSameOrBefore(slotEndTime)) {
                        // remove the slot if the time has passed for the day only from current date
                        if (day.date.toDateString() === new Date().toDateString() && slotStartTime.isBefore(moment())) {
                            continue;
                        }

                        // Check if staff is busy during this time slot
                        const isStaffBusy = schedules.some(schedule => {
                            const scheduleStart = moment(schedule.from, "HH:mm");
                            const scheduleEnd = moment(schedule.to, "HH:mm");
                            const proposedStartMoment = slotStartTime.clone().subtract(durationInMinutes, 'minutes');
                            const proposedEndMoment = slotStartTime.clone();

                            return (
                                (proposedStartMoment.isSameOrAfter(scheduleStart) && proposedStartMoment.isBefore(scheduleEnd)) ||
                                (proposedEndMoment.isAfter(scheduleStart) && proposedEndMoment.isSameOrBefore(scheduleEnd)) ||
                                (proposedStartMoment.isSameOrBefore(scheduleStart) && proposedEndMoment.isSameOrAfter(scheduleEnd))
                            );
                        });

                        if (!isStaffBusy) {
                            processedTimeSlots.push({
                                date: day.date,
                                from: slotStartTime.clone().subtract(durationInMinutes, 'minutes').format('HH:mm'),
                                to: slotStartTime.format('HH:mm'),
                                availabilityStatus: slot.availabilityStatus,
                                privacy: slot.privacy,
                                classType: slot.classType,
                                payRateIds: slot.payRateIds
                            });
                        }
                    }
                }

                if (processedTimeSlots.length > 0) {
                    day.timeSlots = processedTimeSlots;
                    availabilityList.push(item);
                }
            }
        }

        return availabilityList;
    }


    async StaffAvailability(getStaffAvailabilityDto: GetStaffAvailabilityDto, user: any): Promise<any> {
        const { trainerId, facilityId, startDate, endDate, dateRange, from, classType, availabilityStatus } = getStaffAvailabilityDto;

        const filter: any = {
            $and: [{ date: { $lte: new Date(endDate) } }, { date: { $gte: new Date(startDate) } }],
            facilityId: new Types.ObjectId(facilityId),
            "timeSlots.from": from,
            "timeSlots.availabilityStatus": availabilityStatus
        };
        if (user.role.type == ENUM_ROLE_TYPE.ORGANIZATION) {
            filter.organizationId = new Types.ObjectId(user._id);
        }

        if ([ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.TRAINER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN].includes(user.role)) {
            filter.userId = new Types.ObjectId(trainerId);
        }

        if (availabilityStatus === AvailabilityType.AVAILABLE && !classType) {
            throw new BadRequestException("Class Type is required for available status");
        }
        if (classType) {
            filter["timeSlots.classType"] = classType;
        }

        let pipeline = []
        switch (dateRange) {
            case DateRange.SINGLE:
                pipeline = [
                    { $match: filter },
                    {
                        $lookup: {
                            from: "users",
                            localField: "userId",
                            foreignField: "_id",
                            as: "user",
                        },
                    },
                    {
                        $unwind: {
                            path: "$user",
                            preserveNullAndEmptyArrays: true,
                        },
                    },
                    {
                        $addFields: {
                            userName: {
                                $concat: ["$user.firstName", " ", "$user.lastName"],
                            },
                        },
                    },
                    {
                        $addFields: {
                            timeSlot: {
                                $arrayElemAt: [
                                    {
                                        $filter: {
                                            input: "$timeSlots",
                                            as: "slot",
                                            cond: {
                                                $and: [
                                                    { $eq: ["$$slot.from", from] },
                                                    { $eq: ["$$slot.availabilityStatus", availabilityStatus] },
                                                    ...(classType ? [{ $eq: ["$$slot.classType", classType] }] : []),],
                                            },
                                        },
                                    },
                                    0,
                                ],
                            },
                        },
                    },
                    {
                        $project: {
                            _id: 1,
                            userId: 1,
                            organizationId: 1,
                            facilityId: 1,
                            date: 1,
                            userName: 1,
                            startDate: startDate,
                            endDate: endDate,
                            timeSlot: 1,
                        },
                    },
                    {
                        $match: {
                            timeSlot: { $ne: null },
                        },
                    },
                    {
                        $sort: { date: 1 },
                    },
                ];
                break;
            case DateRange.MULTIPLE:
                if (!from) {
                    throw new BadRequestException("From time is required for MULTIPLE date range.");
                }
                pipeline = [
                    { $match: filter },
                    {
                        $lookup: {
                            from: "users",
                            localField: "userId",
                            foreignField: "_id",
                            as: "user",
                        },
                    },
                    {
                        $unwind: {
                            path: "$user",
                            preserveNullAndEmptyArrays: true,
                        },
                    },
                    {
                        $addFields: {
                            userName: {
                                $concat: ["$user.firstName", " ", "$user.lastName"],
                            },
                        },
                    },
                    {
                        $project: {
                            _id: 1,
                            userId: 1,
                            organizationId: 1,
                            facilityId: 1,
                            date: 1,
                            userName: 1,
                            startDate: startDate,
                            endDate: endDate,
                            timeSlots: {
                                $filter: {
                                    input: "$timeSlots",
                                    as: "slot",
                                    cond: {
                                        $and: [
                                            { $eq: ["$$slot.from", from] },
                                            { $eq: ["$$slot.availabilityStatus", availabilityStatus] },
                                            ...(classType ? [{ $eq: ["$$slot.classType", classType] }] : []),
                                        ],
                                    },
                                },
                            },
                        },
                    },
                    {
                        $match: {
                            timeSlots: { $ne: [] },
                        },
                    },
                    {
                        $sort: { date: 1 },
                    },
                ];
                break;
            default:
                throw new BadRequestException("Invalid Enum Value of Date Range Passed");

        }



        const staffAvailabilities = await this.staffAvailabilityModel.aggregate(pipeline);
        if (DateRange.MULTIPLE == dateRange && staffAvailabilities.length > 0) {
            const weekDays: any = {
                mon: [],
                tue: [],
                wed: [],
                thu: [],
                fri: [],
                sat: [],
                sun: []
            };

            const seenDays = new Set<string>();

            const availabilityMap = new Map<string, any>();
            staffAvailabilities.forEach(item => {
                availabilityMap.set(new Date(item.date).toDateString(), item);
            });

            let current = new Date(startDate);
            const end = new Date(endDate);

            while (current <= end) {
                const dayName = current.toLocaleString("en-US", { weekday: "short" }).toLowerCase();
                if (!seenDays.has(dayName)) {
                    const dateKey = current.toDateString();
                    const item = availabilityMap.get(dateKey);

                    if (item) {
                        if (item.timeSlots && item.timeSlots.length > 0) {
                            const firstSlot = item.timeSlots[0];
                            weekDays[dayName].push({
                                //date: item.date,
                                from: firstSlot.from,
                                to: firstSlot.to,
                                availabilityStatus: firstSlot.availabilityStatus,
                                reason: firstSlot.reason,
                                privacy: firstSlot.privacy,
                                classType: firstSlot.classType,
                                payRateIds: firstSlot.payRateIds,
                                _id: firstSlot._id,
                            });

                        }
                    }

                    seenDays.add(dayName);
                }
                current.setDate(current.getDate() + 1);
            }

            const response = {
                userId: staffAvailabilities[0]?.userId || null,
                organizationId: staffAvailabilities[0]?.organizationId || null,
                facilityId: staffAvailabilities[0]?.facilityId || null,
                userName: staffAvailabilities[0]?.userName || null,
                startDate: startDate,
                endDate: endDate,
                timeSlots: weekDays,
            };
            return response;
        }

        return staffAvailabilities?.length > 0 ? staffAvailabilities[0] : [];
    }

    async getStaffAvailability(id: string): Promise<any> {
        return await this.staffAvailabilityModel.findOne({ _id: new Types.ObjectId(id) });
    }

    async updateStaffAvailability(updateStaffAvailabilityDto: UpdateStaffAvailabilityDto, user: any): Promise<any> {
        let query = { facilityId: { $in: [updateStaffAvailabilityDto.facilityId] } };

        if (user.role.type == ENUM_ROLE_TYPE.ORGANIZATION) {
            query["organizationId"] = user._id;
        }
        if (user.role.type == ENUM_ROLE_TYPE.WEB_MASTER || user.role.type == ENUM_ROLE_TYPE.FRONT_DESK_ADMIN) {
            query["userId"] = user._id;
        }

        const facilityDetails = await this.StaffModel.findOne(query, { organizationId: 1 });
        if (!facilityDetails?.organizationId) {
            throw new BadRequestException("Invalid details passed");
        }

        let organizationId = facilityDetails.organizationId;

        switch (updateStaffAvailabilityDto.dateRange) {
            case DateRange.SINGLE:
                await this.updateSingleDateAvailability(updateStaffAvailabilityDto, organizationId);
                break;
            case DateRange.MULTIPLE:
                await this.updateMultipleDateAvailability(updateStaffAvailabilityDto, organizationId);
                break;
            default:
                throw new BadRequestException("Please provide a correct date range");
        }
    }

    private async updateSingleDateAvailability(updateStaffAvailabilityDto: UpdateStaffAvailabilityDto, organizationId: string) {
        const date = new Date(updateStaffAvailabilityDto.startDate);
        const dayOfWeek = date.toLocaleString("en-US", { weekday: "short" }).toLowerCase();
        const requestedSchedule = updateStaffAvailabilityDto.schedule[dayOfWeek];
        const startOfDay = new Date(date.setHours(0, 0, 0, 0));
        const endOfDay = new Date(date.setHours(23, 59, 59, 999));

        if (updateStaffAvailabilityDto.availabilityStatus === StaffAvailabilityEnum.AVAILABLE) {
            const facilityRecords = await this.FacilityAvailabilityModel.find({
                facilityId: new Types.ObjectId(updateStaffAvailabilityDto.facilityId),
                organizationId: new Types.ObjectId(organizationId),
                $or: [
                    {
                        type: "unavailable",
                        fromDate: { $lte: endOfDay },
                        endDate: { $gte: startOfDay },
                    },
                    {
                        type: "available",
                    },
                ],
            });

            const unavailableRecord = facilityRecords.find((record) => record.type === "unavailable");
            const facilityAvailability = facilityRecords.find((record) => record.type === "available");

            if (unavailableRecord && requestedSchedule?.length > 0) {
                for (const requestedSlot of requestedSchedule) {
                    for (const unavailableSlot of unavailableRecord.time) {
                        if (this.isTimeOverlapping(requestedSlot, unavailableSlot)) {
                            throw new BadRequestException(`Facility is unavailable from ${unavailableSlot.from} to ${unavailableSlot.to} on ${startOfDay.toDateString()}`);
                        }
                    }
                }
            }

            if (!facilityAvailability) {
                throw new BadRequestException("No available working hours found for the facility.");
            }


            const facilityWorkingHours = facilityAvailability.workingHours;
            for (const day in updateStaffAvailabilityDto.schedule) {
                const requestedSlots = updateStaffAvailabilityDto.schedule[day];
                const availableSlots = facilityWorkingHours[day];

                if (!availableSlots && requestedSlots.length > 0) {
                    throw new BadRequestException(`Facility is not available on ${day}.`);
                }

                for (const requestedSlot of requestedSlots) {
                    const slotIsWithinAvailability = availableSlots.some((availableSlot: any) => this.isTimeWithinAvailableSlot(requestedSlot, availableSlot));

                    if (!slotIsWithinAvailability) {
                        const availableSlotsText = availableSlots.map((slot) => `${slot.from} to ${slot.to}`).join(", ");
                        throw new BadRequestException(
                            `Requested slot from ${requestedSlot.from} to ${requestedSlot.to} on ${day} is outside facility's available hours. (Working hours for this day are: ${availableSlotsText}).`,
                        );
                    }
                }
            }
        }
        const existingAvailability = await this.staffAvailabilityModel.findOne({
            date: updateStaffAvailabilityDto.startDate,
            userId: updateStaffAvailabilityDto.userId,
            organizationId: organizationId,
            facilityId: updateStaffAvailabilityDto.facilityId,
        })


        this.validateTimeSlots(updateStaffAvailabilityDto.schedule);
        const transformedTimeSlots = this.transformTimeSlotsForSingle(
            updateStaffAvailabilityDto.schedule,
            updateStaffAvailabilityDto.availabilityStatus,
            updateStaffAvailabilityDto.startDate,
            updateStaffAvailabilityDto.classType,
            updateStaffAvailabilityDto.privacy,
            updateStaffAvailabilityDto.reason,
        );


        existingAvailability?.timeSlots?.forEach((slot) => {
            transformedTimeSlots.forEach((requestedSlot) => {
                if (slot.availabilityStatus === AvailabilityType.AVAILABLE && slot.classType !== requestedSlot.classType && requestedSlot.availabilityStatus === AvailabilityType.AVAILABLE) {
                    if (this.isTimeOverlapping(requestedSlot, slot)) {
                        throw new BadRequestException(`Requested ${requestedSlot.classType} slot from ${requestedSlot.from}–${requestedSlot.to} conflicts with existing ${slot.classType} slot from ${slot.from}–${slot.to}.`);
                    }
                }

            })
            return slot.availabilityStatus == updateStaffAvailabilityDto.availabilityStatus && slot.classType == updateStaffAvailabilityDto.classType
        });

        if (existingAvailability) {
            const updatedTimeSlots = await this.mergeOrSplitTimeSlotsOnUpdate(existingAvailability.timeSlots, transformedTimeSlots, updateStaffAvailabilityDto?.startTime);
            existingAvailability.timeSlots = updatedTimeSlots;
            existingAvailability["updatedAt"] = new Date();
            let schedulesSlots = await this.validateNoScheduleOverlapping(updateStaffAvailabilityDto);
            const dateObj = new Date(updateStaffAvailabilityDto.startDate);
            const dateOnly = dateObj.toISOString().split('T')[0];

            if (schedulesSlots[dateOnly]?.length > 0) {
                const requestedSlots = updatedTimeSlots;
                const scheduledSlot = schedulesSlots[dateOnly];
                if (scheduledSlot && requestedSlots && requestedSlots?.length) {
                    await this.validateSlotsOverlayingWithPayRate(requestedSlots, scheduledSlot, dateOnly, updateStaffAvailabilityDto.userId)
                }
            }
            return await existingAvailability.save();
        }
        else {
            const staffAvailabilitySingle = new this.staffAvailabilityModel({
                userId: updateStaffAvailabilityDto.userId,
                facilityId: updateStaffAvailabilityDto.facilityId,
                organizationId: organizationId,
                dateRange: updateStaffAvailabilityDto.dateRange,
                date: updateStaffAvailabilityDto.startDate,
                timeSlots: transformedTimeSlots,
            });

            return await staffAvailabilitySingle.save();
        }

    }

    private async validateScheduleSlotsOverlaying(slotsA: any[], slotsS: any[]) {
        for (let slotA of slotsA) {
            const a1 = new Date(`1970-01-01T${slotA.from}:00Z`);
            const a2 = new Date(`1970-01-01T${slotA.to}:00Z`);
            for (let slotS of slotsS) {
                const s1 = new Date(`1970-01-01T${slotS.from}:00Z`);
                const s2 = new Date(`1970-01-01T${slotS.to}:00Z`);
                if (a1 <= s1 && a2 >= s2) {
                    return slotS
                }
            }
        }
        return false
    }

    private async validateSlotsOverlayingWithPayRateV0(requestedSlot: any[], scheduledSlots: any[], day: string, userId: any) {
        // Fetch all relevant pay rates for the user in a single query
        const userPayRates = await this.PayRateModel.find({
            userId: userId,
            $or: scheduledSlots.map(slot => ({
                serviceCategory: slot.serviceCategoryId,
                appointmentType: slot.subTypeId
            }))
        }, { _id: 1, serviceCategory: 1, appointmentType: 1 });

        // Create a map for quick pay rate lookup
        const payRateMap = new Map(
            userPayRates.map(rate => [
                `${rate.serviceCategory}-${rate.appointmentType}`,
                rate._id.toString()
            ])
        );

        for (let slotA of requestedSlot) {
            const a1 = new Date(`1970-01-01T${slotA.from}:00Z`);
            const a2 = new Date(`1970-01-01T${slotA.to}:00Z`);

            let matchingSlot = false;
            const requestedPayRateIds = new Set(slotA.payRateIds);

            for (let slotS of scheduledSlots) {
                const s1 = new Date(`1970-01-01T${slotS.from}:00Z`);
                const s2 = new Date(`1970-01-01T${slotS.to}:00Z`);

                // Check if requested slot fully includes scheduled slot
                if (a1 <= s1 && a2 >= s2) {
                    matchingSlot = true;

                    // Use precomputed pay rate map
                    const payRateKey = `${slotS.serviceCategoryId}-${slotS.subTypeId}`;
                    const scheduledPayRateId = payRateMap.get(payRateKey);

                    if (scheduledPayRateId && !requestedPayRateIds.has(scheduledPayRateId)) {
                        throw new BadRequestException(
                            `Service category mismatch for scheduled slot from ${slotS.from} to ${slotS.to} on ${dayAbbreviation[day]}`
                        );
                    }
                }
            }

            // If no matching slot is found for a requested slot
            if (!matchingSlot) {
                throw new BadRequestException(
                    `Requested time slot from ${slotA.from} to ${slotA.to} on ${dayAbbreviation[day]} does not cover existing scheduled slot`
                );
            }
        }
        return false;
    }

    private async validateSlotsOverlayingWithPayRate(
        requestedSlots: any[],
        scheduledSlots: any[],
        date: string,
        userId: any
    ) {
        if (!scheduledSlots || scheduledSlots.length === 0) {
            return false;
        }

        if (!requestedSlots || requestedSlots.length === 0) {
            throw new BadRequestException(
                `No availability slots provided for ${date} but there are scheduled slots that need coverage`
            );
        }

        const userPayRates = await this.PayRateModel.find({
            userId: userId,
            $or: scheduledSlots.map(slot => ({
                serviceCategory: slot.serviceCategoryId,
                appointmentType: slot.subTypeId
            }))
        }, { _id: 1, serviceCategory: 1, appointmentType: 1 });

        const payRateMap = new Map(
            userPayRates.map(rate => [
                `${rate.serviceCategory}-${rate.appointmentType}`,
                rate._id.toString()
            ])
        );

        for (const scheduledSlot of scheduledSlots) {
            const scheduledStart = new Date(`1970-01-01T${scheduledSlot.from}:00Z`);
            const scheduledEnd = new Date(`1970-01-01T${scheduledSlot.to}:00Z`);
            const payRateKey = `${scheduledSlot.serviceCategoryId}-${scheduledSlot.subTypeId}`;
            const scheduledPayRateId = payRateMap.get(payRateKey);

            let hasOverlap = false;
            let hasMatchingPayRate = false;
            let isFullyCovered = false;

            for (const requestedSlot of requestedSlots) {
                const requestedStart = new Date(`1970-01-01T${requestedSlot.from}:00Z`);
                const requestedEnd = new Date(`1970-01-01T${requestedSlot.to}:00Z`);
                const requestedPayRateIds = new Set(requestedSlot?.payRateIds?.map(id => id?.toString()) || []);
                const availabilityStatus = requestedSlot.availabilityStatus;

                const isOverlapping = requestedStart < scheduledEnd && requestedEnd > scheduledStart && availabilityStatus === AvailabilityType.AVAILABLE;
                if (!isOverlapping) continue;

                hasOverlap = true;

                const payRateMatches = scheduledPayRateId
                    ? requestedPayRateIds.has(scheduledPayRateId)
                    : true;

                if (payRateMatches) {
                    hasMatchingPayRate = true;

                    const timeCovers = requestedStart <= scheduledStart && requestedEnd >= scheduledEnd;
                    if (availabilityStatus === AvailabilityType.AVAILABLE && timeCovers) {
                        isFullyCovered = true;
                        break;
                    }
                }
            }

            if (!hasOverlap) {
                throw new BadRequestException(
                    `Scheduled slot from ${scheduledSlot.from} to ${scheduledSlot.to} on ${date} has no overlapping requested availability slots`
                );
            }

            if (!hasMatchingPayRate) {
                throw new BadRequestException(
                    `Scheduled slot from ${scheduledSlot.from} to ${scheduledSlot.to} on ${date} has no matching pay rate in requested availability`
                );
            }

            if (!isFullyCovered) {
                throw new BadRequestException(
                    `Scheduled slot from ${scheduledSlot.from} to ${scheduledSlot.to} on ${date} is not fully covered by any AVAILABLE requested slot`
                );
            }
        }

        return false;
    }

    private async validateNoScheduleOverlapping(updateStaffAvailabilityDto: UpdateStaffAvailabilityDto) {
        const agg: PipelineStage[] = [
            {
                $match: {
                    trainerId: new Types.ObjectId(updateStaffAvailabilityDto.userId),
                    facilityId: new Types.ObjectId(updateStaffAvailabilityDto.facilityId),
                    scheduleStatus: { $ne: ScheduleStatusType.CANCELED },
                    classType: ClassType.PERSONAL_APPOINTMENT,
                    date: { $gte: updateStaffAvailabilityDto.startDate, $lte: updateStaffAvailabilityDto.endDate },
                }
            },
            {
                $project: {
                    dateOnly: { $dateToString: { format: "%Y-%m-%d", date: "$date" } }, // Extract date without time
                    from: 1,
                    to: 1,
                    serviceCategoryId: 1,
                    subTypeId: 1
                }
            },
            {
                $group: {
                    _id: '$dateOnly',
                    timeSlots: {
                        $addToSet: {
                            from: '$from',
                            to: '$to',
                            serviceCategoryId: '$serviceCategoryId',
                            subTypeId: '$subTypeId'
                        }
                    }
                }
            },
            {
                $group: {
                    _id: null,
                    data: {
                        $push: {
                            k: '$_id',
                            v: '$timeSlots'
                        }
                    }
                }
            },
            {
                $replaceRoot: {
                    newRoot: {
                        $arrayToObject: '$data'
                    }
                }
            }
        ];

        const schedulesSlots = await this.SchedulingModel.aggregate(agg);
        return schedulesSlots.length > 0 ? schedulesSlots[0] : {};
    }
    private async updateMultipleDateAvailability(updateStaffAvailabilityDto: UpdateStaffAvailabilityDto, organizationId: string) {
        const session = await this.staffAvailabilityModel.startSession();
        session.startTransaction();
        // try {
        let { startDate, endDate, userId, facilityId, availabilityStatus, schedule, privacy, reason, classType, startTime, markType } = updateStaffAvailabilityDto;

        if (markType === MarkAvailabilityType.CUSTOM && !endDate) {
            throw new BadRequestException("End date is required for custom date range");
        }
        if (markType === MarkAvailabilityType.WEEKLY) {
            endDate = new Date(startDate);
            endDate.setFullYear(endDate.getFullYear() + 1);
        }
        if (startDate > endDate) {
            throw new BadRequestException("Start date should be less than end date");
        }

        if (availabilityStatus === StaffAvailabilityEnum.AVAILABLE) {
            const Sdate = new Date(startDate);
            const Edate = new Date(endDate);
            const facilityRecords = await this.FacilityAvailabilityModel.find({
                facilityId: new Types.ObjectId(facilityId),
                organizationId: new Types.ObjectId(organizationId),
                $or: [
                    { type: "unavailable", fromDate: { $lte: Edate }, endDate: { $gte: Sdate } },
                    { type: "available" }
                ]
            });

            const facilityAvailability = facilityRecords.find(r => r.type === "available");
            if (!facilityAvailability) {
                throw new BadRequestException("No available working hours found for the facility.");
            }

            let unavailableRecord = facilityRecords.find(r => r.type === "unavailable");

            if (unavailableRecord && schedule) {
                let checkDate = new Date(Sdate);
                while (checkDate <= Edate) {
                    const dayOfWeek = checkDate.toLocaleDateString("en-US", { weekday: "short" }).toLowerCase();
                    unavailableRecord = facilityRecords.find(r => r.fromDate <= checkDate && r.endDate >= checkDate && r.type === "unavailable");
                    const requestedSchedule = schedule[dayOfWeek];
                    if (unavailableRecord && requestedSchedule?.length > 0) {
                        for (const requestedSlot of requestedSchedule) {
                            for (const unavailableSlot of unavailableRecord.time) {
                                if (this.isTimeOverlapping(requestedSlot, unavailableSlot)) {
                                    throw new BadRequestException(`Facility is unavailable from ${unavailableSlot.from} to ${unavailableSlot.to} on ${checkDate.toDateString()}`);
                                }
                            }
                        }
                    }
                    checkDate.setUTCDate(checkDate.getUTCDate() + 1);
                }
            }

            const facilityWorkingHours = facilityAvailability.workingHours;
            for (const day in schedule) {
                const requestedSlots = schedule[day];
                const availableSlots = facilityWorkingHours[day];
                if (!availableSlots && requestedSlots.length > 0) {
                    throw new BadRequestException(`Facility is not available on ${day}.`);
                }
                for (const requestedSlot of requestedSlots) {
                    const slotIsWithin = availableSlots?.some((availableSlot: any) => this.isTimeWithinAvailableSlot(requestedSlot, availableSlot));
                    if (!slotIsWithin) {
                        const availableSlotsText = availableSlots?.map((slot) => `${slot.from} to ${slot.to}`).join(", ") || "";
                        throw new BadRequestException(
                            `Requested slot from ${requestedSlot.from} to ${requestedSlot.to} on ${day} is outside facility's available hours. (Working hours for this day are: ${availableSlotsText}).`
                        );
                    }
                }
            }
        }

        this.validateTimeSlots(schedule);

        const allSchedules = await this.staffAvailabilityModel
            .find({
                date: { $gte: new Date(startDate), $lte: new Date(endDate) },
                facilityId: new Types.ObjectId(facilityId),
                organizationId: new Types.ObjectId(organizationId),
                userId: new Types.ObjectId(userId),
            })
            .sort({ date: 1 })
            .session(session);

        const scheduleMap: Record<string, any> = {};
        allSchedules.forEach(sch => {
            scheduleMap[sch.date.toISOString()] = sch;
        });

        const overlappingSchedules = allSchedules?.filter(schedule => {
            const hasMatchingTimeSlot = startTime
                ? schedule?.timeSlots?.some(slot => {
                    const matchesStartTime = slot?.from === startTime;
                    const matchesAvailability = slot?.availabilityStatus === availabilityStatus;
                    const matchesClassType =
                        availabilityStatus === StaffAvailabilityEnum.AVAILABLE
                            ? slot?.classType === classType
                            : true;

                    return matchesStartTime && matchesAvailability && matchesClassType;
                })
                : true;

            return hasMatchingTimeSlot;
        });

        const OverlapscheduleMap: Record<string, any> = {};
        overlappingSchedules.forEach(sch => {
            OverlapscheduleMap[sch.date.toISOString()] = sch;
        });

        const insertPromises = [];
        let currentDate = new Date(startDate);
        const end = new Date(endDate);
        let schedulesSlots = await this.validateNoScheduleOverlapping(updateStaffAvailabilityDto);

        while (currentDate <= end) {
            const dayOfWeek = currentDate.toLocaleDateString("en-US", { weekday: "short" }).toLowerCase();
            const currentDayTimeSlots = schedule[dayOfWeek]
                ? this.transformTimeSlotsForMultiple(schedule[dayOfWeek], availabilityStatus, classType, privacy, reason)
                : [];

            if (currentDayTimeSlots.length > 0) {
                const currentDateISO = currentDate.toISOString();
                const existingSchedule = scheduleMap[currentDateISO];
                const existingOverlappingSchedule = OverlapscheduleMap[currentDateISO];

                if (existingOverlappingSchedule) {
                    existingSchedule.timeSlots?.forEach((slot) => {
                        currentDayTimeSlots.forEach((requestedSlot) => {
                            if (
                                slot.availabilityStatus === AvailabilityType.AVAILABLE &&
                                slot.classType !== requestedSlot.classType &&
                                requestedSlot.availabilityStatus === AvailabilityType.AVAILABLE &&
                                this.isTimeOverlapping(requestedSlot, slot)
                            ) {
                                throw new BadRequestException(`Requested ${requestedSlot.classType} slot from ${requestedSlot.from}–${requestedSlot.to} conflicts with existing ${slot.classType} slot from ${slot.from}–${slot.to}.`);
                            }
                        });
                    });

                    const updatedTimeSlots = await this.mergeOrSplitTimeSlotsOnUpdate(
                        existingSchedule.timeSlots,
                        currentDayTimeSlots,
                        startTime
                    );
                    existingSchedule.timeSlots = updatedTimeSlots;
                    existingSchedule["updatedAt"] = new Date();
                    const dateObj = new Date(currentDate);
                    const dateOnly = dateObj.toISOString().split('T')[0];
                    if (schedulesSlots[dateOnly]?.length > 0) {
                        const requestedSlots = updatedTimeSlots;
                        const scheduledSlot = schedulesSlots[dateOnly];
                        if (scheduledSlot && requestedSlots && requestedSlots?.length) {
                            await this.validateSlotsOverlayingWithPayRate(requestedSlots, scheduledSlot, dateOnly, updateStaffAvailabilityDto.userId)
                        }
                    }
                    insertPromises.push(existingSchedule.save({ session }));
                }
                else if (!existingOverlappingSchedule && existingSchedule) {
                    existingSchedule.timeSlots?.forEach((slot) => {
                        currentDayTimeSlots.forEach((requestedSlot) => {
                            if (
                                slot.availabilityStatus === AvailabilityType.AVAILABLE &&
                                slot.classType !== requestedSlot.classType &&
                                requestedSlot.availabilityStatus === AvailabilityType.AVAILABLE &&
                                this.isTimeOverlapping(requestedSlot, slot)
                            ) {
                                throw new BadRequestException(`Requested ${requestedSlot.classType} slot from ${requestedSlot.from}–${requestedSlot.to} conflicts with existing ${slot.classType} slot from ${slot.from}–${slot.to}.`);
                            }
                        });
                    });

                    const updatedTimeSlots = await this.mergeOrSplitTimeSlotsOnUpdate(
                        existingSchedule.timeSlots,
                        currentDayTimeSlots,
                    );
                    existingSchedule.timeSlots = updatedTimeSlots;
                    existingSchedule["updatedAt"] = new Date();
                    const dateObj = new Date(currentDate);
                    const dateOnly = dateObj.toISOString().split('T')[0];
                    if (schedulesSlots[dateOnly]?.length > 0) {
                        const requestedSlots = updatedTimeSlots;
                        const scheduledSlot = schedulesSlots[dateOnly];
                        if (scheduledSlot && requestedSlots && requestedSlots?.length) {
                            await this.validateSlotsOverlayingWithPayRate(requestedSlots, scheduledSlot, dateOnly, updateStaffAvailabilityDto.userId)
                        }
                    }
                    insertPromises.push(existingSchedule.save({ session }));

                }
                else {
                    insertPromises.push(
                        this.staffAvailabilityModel.create(
                            [
                                {
                                    userId: new Types.ObjectId(userId),
                                    organizationId: new Types.ObjectId(organizationId),
                                    facilityId: new Types.ObjectId(facilityId),
                                    dateRange: updateStaffAvailabilityDto.dateRange,
                                    date: new Date(currentDate),
                                    timeSlots: currentDayTimeSlots,
                                }
                            ],
                            { session }
                        )
                    );
                }
            }
            currentDate.setUTCDate(currentDate.getUTCDate() + 1);
        }

        await Promise.all(insertPromises);
        await session.commitTransaction();
        return "Multiple date availability updated successfully";
    }


    // private async updateMultipleDateAvailability(updateStaffAvailabilityDto: UpdateStaffAvailabilityDto,organizationId: string) {
    //     const session = await this.staffAvailabilityModel.startSession(); // Start a transaction session
    //     session.startTransaction();
    //     try {
    //     // const date = new Date(updateStaffAvailabilityDto.startDate);
    //     // const dayOfWeek = date.toLocaleString("en-US", { weekday: "short" }).toLowerCase();
    //     // const requestedSchedule = updateStaffAvailabilityDto.schedule[dayOfWeek];
    //     // const startOfDay = new Date(date.setHours(0, 0, 0, 0));
    //     // const endOfDay = new Date(date.setHours(23, 59, 59, 999));

    //     // const facilityRecords = await this.FacilityAvailabilityModel.find({
    //     //     facilityId: new Types.ObjectId(updateStaffAvailabilityDto.facilityId),
    //     //     organizationId: new Types.ObjectId(organizationId),
    //     //     $or: [
    //     //         {
    //     //             type: "unavailable",
    //     //             fromDate: { $lte: endOfDay },
    //     //             endDate: { $gte: startOfDay },
    //     //         },
    //     //         {
    //     //             type: "available",
    //     //         },
    //     //     ],
    //     // });

    //     // const unavailableRecord = facilityRecords.find((record) => record.type === "unavailable");
    //     // const facilityAvailability = facilityRecords.find((record) => record.type === "available");

    //     // if (unavailableRecord && requestedSchedule?.length > 0) {
    //     //     for (const requestedSlot of requestedSchedule) {
    //     //         for (const unavailableSlot of unavailableRecord.time) {
    //     //             if (this.isTimeOverlapping(requestedSlot, unavailableSlot)) {
    //     //                 throw new BadRequestException(`Facility is unavailable from ${unavailableSlot.from} to ${unavailableSlot.to}`);
    //     //             }
    //     //         }
    //     //     }
    //     // }

    //     // if (!facilityAvailability) {
    //     //     throw new BadRequestException("No available working hours found for the facility.");
    //     // }

    //     // const facilityWorkingHours = facilityAvailability.workingHours;
    //     // for (const day in updateStaffAvailabilityDto.schedule) {
    //     //     const requestedSlots = updateStaffAvailabilityDto.schedule[day];
    //     //     const availableSlots = facilityWorkingHours[day];

    //     //     if (!availableSlots && requestedSlots.length > 0) {
    //     //         throw new BadRequestException(`Facility is not available on ${day}.`);
    //     //     }

    //     //     for (const requestedSlot of requestedSlots) {
    //     //         const slotIsWithinAvailability = availableSlots.some((availableSlot: any) => this.isTimeWithinAvailableSlot(requestedSlot, availableSlot));

    //     //         if (!slotIsWithinAvailability) {
    //     //             const availableSlotsText = availableSlots.map((slot) => `${slot.from} to ${slot.to}`).join(", ");
    //     //             throw new BadRequestException(
    //     //                 `Requested slot from ${requestedSlot.from} to ${requestedSlot.to} on ${day} is outside facility's available hours. (Working hours for this day are: ${availableSlotsText}).`,
    //     //             );
    //     //         }
    //     //     }
    //     // }
    //     const {startDate,endDate,userId,facilityId,availabilityStatus,schedule,privacy,reason,classType,startTime,endTime} = updateStaffAvailabilityDto;
    //     if (startDate > endDate) {
    //         throw new BadRequestException("Start date should be less than end date");
    //     }

    //     const allSchedules = await this.staffAvailabilityModel
    //     .find({
    //         $and: [
    //             { date: { $lte: new Date(endDate) } },
    //             { date: { $gte: new Date(startDate) } }
    //         ],
    //         facilityId: new Types.ObjectId(facilityId),
    //         organizationId: new Types.ObjectId(organizationId),
    //         userId: new Types.ObjectId(userId)
    //     })
    //     .sort({ date: 1 })
    //     .session(session);

    // // Separate schedules based on whether they have the specified time slot
    // // const { withTimeSlots, withoutTimeSlots } = allSchedules.reduce(
    // //     (result, schedule) => {
    // //         const hasTimeSlot = schedule.timeSlots?.some(
    // //             (slot: { from: string; to: string }) =>
    // //                 slot.from === startTime && slot.to === endTime
    // //         );

    // //         if (hasTimeSlot) {
    // //             result.withTimeSlots.push(schedule);
    // //         } else {
    // //             result.withoutTimeSlots.push(schedule);
    // //         }

    // //         return result;
    // //     },
    // //     { withTimeSlots: [], withoutTimeSlots: [] } as {
    // //         withTimeSlots: typeof allSchedules;
    // //         withoutTimeSlots: typeof allSchedules;
    // //     }
    // // );

    // //     const existingDataOfSameSlot = withTimeSlots.reduce((acc, schedule) => {
    // //         acc[schedule._id] = schedule.date.toISOString(); 
    // //         return acc;
    // //     }, {} as Record<string, string>);

    // //     const existingDataOfDifferentSlot = withoutTimeSlots.reduce((acc, schedule) => {
    // //         acc[schedule._id] = schedule.date.toISOString(); 
    // //         return acc;
    // //     }, {} as Record<string, string>);

    //     this.validateTimeSlots(schedule);

    //     const insertPromises = [];
    //     let currentDate = new Date(startDate);
    //     const end = new Date(endDate);

    //     while (currentDate <= end) {
    //         const dayOfWeek = currentDate
    //             .toLocaleDateString("en-US", { weekday: "short" })
    //             .toLowerCase();

    //         if (schedule[dayOfWeek]) {
    //             const currentDayTimeSlots = this.transformTimeSlotsForMultiple(
    //                 schedule[dayOfWeek],
    //                 availabilityStatus,
    //                 classType,
    //                 privacy,
    //                 reason,
    //             );

    //             if (currentDayTimeSlots?.length > 0) {
    //                 const currentDateISO = currentDate.toISOString();
    //                 const existingIdWithSlot = Object.keys(existingDataOfSameSlot).find(
    //                     key => existingDataOfSameSlot[key] === currentDateISO,
    //                 );
    //                 // const existingIdWithoutSlot = Object.keys(existingDataOfDifferentSlot).find(
    //                 //     key => existingDataOfDifferentSlot[key] === currentDateISO,
    //                 // );


    //                 if (existingIdWithSlot) {
    //                     const existingSchedule = withoutTimeSlots.find(
    //                         sch => sch._id.toString() === existingIdWithSlot.toString(),
    //                     );

    //                     if (existingSchedule) {
    //                         const updatedTimeSlots = this.mergeOrSplitTimeSlots(
    //                             existingSchedule.timeSlots,
    //                             currentDayTimeSlots,
    //                         );
    //                         existingSchedule.timeSlots = updatedTimeSlots;
    //                         existingSchedule["updatedAt"] = new Date();

    //                         insertPromises.push(existingSchedule.save({ session }));
    //                     }
    //                 } 

    //                 // else {
    //                 //     const staffAvailability = new this.staffAvailabilityModel({
    //                 //         userId: new Types.ObjectId(userId),
    //                 //         facilityId: new Types.ObjectId(facilityId),
    //                 //         organizationId: new Types.ObjectId(organizationId),
    //                 //         dateRange: DateRange.MULTIPLE,
    //                 //         date: new Date(currentDate),
    //                 //         timeSlots: currentDayTimeSlots,
    //                 //     });
    //                 //     insertPromises.push(staffAvailability.save({ session }));
    //                 // }
    //             }

    //     }
    //         currentDate.setUTCDate(currentDate.getUTCDate() + 1);
    // }

    //     await Promise.all(insertPromises);

    //     await session.commitTransaction();
    //     return "Multiple date availability created successfully";
    // } catch (error) {
    //     await session.abortTransaction();
    //     console.error("Error creating multiple date availability:", error);
    //     throw error instanceof BadRequestException
    //         ? error
    //         : new InternalServerErrorException(
    //             "An error occurred while creating multiple date availability. Please try again later.",
    //         );
    // } finally {
    //     session.endSession();
    // }
    // }


    async deleteStaffAvailability(availabilityDto: DeleteStaffAvailabilityDto, user: any): Promise<any> {
        const { userId, slotId, startDate, endDate, facilityId, type, dateId } = availabilityDto;

        let query: any = {};

        if (user.role.type == ENUM_ROLE_TYPE.ORGANIZATION) {
            query.organizationId = new Types.ObjectId(user._id);
        }

        if (type === DeleteStaffAvailability.SLOT) {
            query["_id"] = new Types.ObjectId(dateId);
            query["timeSlots._id"] = new Types.ObjectId(slotId);

            const document = await this.staffAvailabilityModel.findById(query, { date: 1, timeSlots: 1, userId: 1 });
            const scheduleSlots = await this.SchedulingModel.find({
                trainerId: document.userId,
                date: { $gte: document.date, $lte: document.date },
                scheduleStatus: { $ne: ScheduleStatusType.CANCELED }
            }, {
                from: "$from",
                to: "$to"
            })
            if (await this.validateScheduleSlotsOverlaying(document.timeSlots, scheduleSlots,)) {
                throw new BadRequestException("There is a booking already scheduled in this slot ")
            }
            if (document && document.timeSlots.length === 1) {
                return this.staffAvailabilityModel.deleteOne(query);
            } else {
                return this.staffAvailabilityModel.updateOne(query, { $pull: { timeSlots: { _id: new Types.ObjectId(slotId) } } });
            }
        } else if (type === DeleteStaffAvailability.CUSTOM) {
            (query.facilityId = new Types.ObjectId(facilityId)), (query.userId = new Types.ObjectId(userId)), (query.date = { $gte: startDate, $lte: endDate });
            const count = await this.SchedulingModel.count({ facilityId: facilityId, trainerId: userId, date: { $gte: startDate, $lte: endDate }, scheduleStatus: { $ne: ScheduleStatusType.CANCELED } })
            if (count) {
                throw new BadRequestException(`There is ${count} booking(s) scheduled on the selected date range`)
            }
            return this.staffAvailabilityModel.deleteMany(query);
        }
    }

    async findStaffAvailabilityById(availabilityDto: DeleteStaffAvailabilityDto, user: any): Promise<any> {
        const { userId, slotId, startDate, endDate, facilityId, type, dateId } = availabilityDto;
        let query: any = {};
        if (user.role.type == ENUM_ROLE_TYPE.ORGANIZATION) {
            query.organizationId = new Types.ObjectId(user._id);
        }
        if (type === DeleteStaffAvailability.CUSTOM) {
            (query.facilityId = new Types.ObjectId(facilityId)), (query.userId = new Types.ObjectId(userId)), (query.date = { $gte: startDate, $lte: endDate });
        }
        if (type === DeleteStaffAvailability.SLOT) {
            query["timeSlots._id"] = new Types.ObjectId(slotId);
            query._id = new Types.ObjectId(dateId);
        }
        return this.staffAvailabilityModel.find(query);
    }

    async availServiceCategory(requestDto: AvailabilityServiceCategoryDto, user: any): Promise<any> {
        const { organizationId, facilityIds, trainerIds, classType, search } = requestDto;

        if (!organizationId) {
            throw new BadRequestException("Organization Id is required");
        }

        let query: any = {
            organizationId: new Types.ObjectId(organizationId),
        };

        if (facilityIds?.length > 0) {
            query.facilityId = {
                $in: facilityIds.map((id) => new Types.ObjectId(id)),
            };
        }

        if (trainerIds?.length > 0) {
            query.userId = {
                $in: trainerIds.map((id) => new Types.ObjectId(id)),
            };
        }

        const staffAvailability = await this.staffAvailabilityModel.aggregate([
            {
                $match: query,
            },
            {
                $addFields: {
                    timeSlots: {
                        $filter: {
                            input: "$timeSlots",
                            as: "slot",
                            cond: classType
                                ? { $eq: ["$$slot.classType", classType] } // Match if classType is provided
                                : true, // Otherwise include all
                        },
                    },
                },
            },
            {
                $project: {
                    payRateIds: {
                        $reduce: {
                            input: "$timeSlots",
                            initialValue: [],
                            in: { $setUnion: ["$$value", "$$this.payRateIds"] },
                        },
                    },
                },
            },
            { $unwind: "$payRateIds" },
            {
                $lookup: {
                    from: "payrates",
                    localField: "payRateIds",
                    foreignField: "_id",
                    as: "payRateDetails",
                },
            },
            { $unwind: "$payRateDetails" },
            {
                $match: classType
                    ? { "payRateDetails.serviceType": classType } // Match if classType is provided
                    : {}, // Skip if classType is not provided
            },
            {
                $group: {
                    _id: null,
                    payRateServiceDetails: {
                        $addToSet: {
                            payRateId: "$payRateDetails._id",
                            serviceCategory: "$payRateDetails.serviceCategory",
                        },
                    },
                },
            },
            { $unwind: "$payRateServiceDetails" },
            {
                $lookup: {
                    from: "services",
                    localField: "payRateServiceDetails.serviceCategory",
                    foreignField: "_id",
                    as: "serviceDetails",
                },
            },
            { $unwind: "$serviceDetails" },
            {
                $match: search
                    ? { "serviceDetails.name": { $regex: search, $options: "i" } }
                    : {}, // Filter by search if provided
            },
            {
                $group: {
                    _id: null,
                    services: {
                        $addToSet: {
                            payRateId: "$payRateServiceDetails.payRateId",
                            name: "$serviceDetails.name",
                        },
                    },
                },
            },
        ]);
        return {
            message: "Success",
            data: staffAvailability.length > 0 ? staffAvailability[0].services : [],
        }
    }
    async checkMailorMobile(data: CheckMailorPhoneDto): Promise<boolean> {
        const { value, id } = data;

        const query: any = {
            $and: [
                {
                    $or: [
                        {
                            parent: { $exists: false }
                        },
                        {
                            parent: null
                        },
                    ],
                },
                {
                    $or: [
                        { email: value.toLowerCase() },
                        { mobile: value },
                    ]
                },

            ],
        };

        if (id) {
            query['$and'] = [
                ...query['$and'],
                { _id: { $ne: new Types.ObjectId(id) } },
                { parent: { $ne: new Types.ObjectId(id) } }
            ];
        }

        const user = await this.UserModel.findOne(query);
        return !!user;
    }

    async encryptPin(pin: string, organizationId: IDatabaseObjectId | string): Promise<string> {
        const salt = `$2b$10$${organizationId.toString().padEnd(22, '.')}`;
        console.log(bcrypt.hash(pin, salt));

        return bcrypt.hash(pin, salt);
    }

    async setPin(organizationId: IDatabaseObjectId, setPinDto: ValidatePinDto): Promise<Boolean> {
        const { pin, confirmPin } = setPinDto;
        const encryptedPin = await this.encryptPin(pin, organizationId);

        if (pin !== confirmPin) {
            throw new BadRequestException('PINs do not match');
        }

        // if (pin.length !== 4) {
        //     throw new BadRequestException('PIN must be 4 digits long');
        // }

        const user = await this.UserModel.findById(new Types.ObjectId(setPinDto.userId), '+pin');
        if (!user) {
            throw new NotFoundException('User not found');
        }

        // Optimized query to check PIN uniqueness
        const pinCheck = await this.StaffModel.aggregate([
            {
                $match: {
                    organizationId: new Types.ObjectId(organizationId)

                }
            },
            {
                $lookup: {
                    from: 'users',
                    localField: 'userId',
                    foreignField: '_id',
                    as: 'user'
                }
            },
            {
                $unwind: '$user'
            },
            {
                $match: {
                    'user.pin': encryptedPin,
                }
            },
            {
                $limit: 1
            }
        ]);

        if (pinCheck.length > 0) {
            throw new BadRequestException("Cannot use this pin as it is already in use by another staff member in this organization");
        }

        user.pin = encryptedPin;
        await user.save();

        return true;
    }

    async trainersListV1(body: TrainersListV1Dto, user: any) {
        const { classType, facilityId, serviceId, subTypeId, date, startTime, endTime } = body;
        const fromDate = new Date(`1970-01-01T${startTime}:00`);
        const toDate = new Date(`1970-01-01T${endTime}:00`);
        const startDate = new Date(date);
        const startOfDay = new Date(startDate.setHours(0, 0, 0, 0));
        const endOfDay = new Date(startDate.setHours(23, 59, 59, 999));

        if (fromDate > toDate) {
            throw new BadRequestException(`Start time (${startTime}) must be less than endTime time (${endTime})`);
        }
        let pipeline: any = [
            {
                $match: {
                    facilityId: { $in: [new Types.ObjectId(facilityId)] }
                }
            },
            {
                $project: { userId: 1 }
            },
            {
                $lookup: {
                    from: "users",
                    localField: "userId",
                    foreignField: "_id",
                    as: "staffDetails"
                    // pipeline: [
                    //     {
                    //         $match: {
                    //             isActive: true
                    //         }
                    //     },
                    //     {
                    //         $lookup: {
                    //             from: RoleTableName,
                    //             localField: "role",
                    //             foreignField: "_id",
                    //             as: "roleDetails"
                    //         }
                    //     },
                    //     {
                    //         $match: {
                    //             "roleDetails.0.type": ENUM_ROLE_TYPE.TRAINER
                    //         }
                    //     }
                    // ]
                }
            },
            {
                $unwind: {
                    path: "$staffDetails",
                    preserveNullAndEmptyArrays: false
                }
            },
            {
                $match: {
                    "staffDetails.isActive": true
                }
            },
            {
                $lookup: {
                    from: "payrates",
                    localField: "userId",
                    foreignField: "userId",
                    as: "payRates"
                }
            },
            {
                $unwind: {
                    path: "$payRates",
                    preserveNullAndEmptyArrays: false
                }
            },
            {
                $match: {
                    "payRates.serviceType": classType,
                    "payRates.serviceCategory": new Types.ObjectId(serviceId),
                    "payRates.appointmentType": new Types.ObjectId(subTypeId)
                }
            },
            {
                $group: {
                    _id: "$userId",
                    firstName: { $first: "$staffDetails.firstName" },
                    lastName: { $first: "$staffDetails.lastName" },
                    email: { $first: "$staffDetails.email" },
                    isActive: { $first: "$staffDetails.isActive" },
                    payRateIds: { $push: "$payRates._id" }
                }
            },
            {
                $lookup: {
                    from: "staffavailabilities",
                    localField: "_id",
                    foreignField: "userId",
                    as: "staffAvailabilities"
                }
            },
            {
                $unwind: {
                    path: "$staffAvailabilities",
                    preserveNullAndEmptyArrays: true
                }
            },
            {
                $match: {
                    "staffAvailabilities.date": { $lte: endOfDay, $gte: startOfDay },
                    "staffAvailabilities.facilityId": new Types.ObjectId(facilityId)
                },
            },
            {
                $set: {
                    "staffAvailabilities.timeSlots": {
                        $filter: {
                            input: "$staffAvailabilities.timeSlots",
                            as: "slot",
                            cond: {
                                $and: [
                                    { $eq: ["$$slot.availabilityStatus", StaffAvailabilityEnum.AVAILABLE] },
                                    { $eq: ["$$slot.classType", classType] },
                                    {
                                        $gt: [
                                            { $size: { $setIntersection: ["$$slot.payRateIds", "$payRateIds"] } },
                                            0
                                        ]
                                    },
                                    {
                                        $lte: ["$$slot.from", startTime]
                                    },
                                    {
                                        $gte: ["$$slot.to", endTime]
                                    }
                                ]
                            }
                        }
                    }
                }
            },
            {
                $lookup: {
                    from: "schedulings",
                    let: { trainerId: "$_id" },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $and: [
                                        { $eq: ["$facilityId", new Types.ObjectId(facilityId)] },
                                        { $eq: ["$trainerId", "$$trainerId"] },
                                        { $ne: ["$scheduleStatus", ScheduleStatusType.CANCELED] },
                                        { $gte: ["$date", startOfDay] },
                                        { $lte: ["$date", endOfDay] },
                                        { $lt: ["$from", endTime] },
                                        { $gt: ["$to", startTime] }
                                    ]
                                }
                            }
                        }
                    ],
                    as: "trainerSchedules"
                }
            },
            {
                $set: {
                    isAvailable: {
                        $cond: {
                            if: {
                                $and: [
                                    { $gt: [{ $size: "$staffAvailabilities.timeSlots" }, 0] },
                                    { $eq: [{ $size: "$trainerSchedules" }, 0] }
                                ]
                            },
                            then: true,
                            else: false
                        }
                    },
                    reason: {
                        $cond: {
                            if: { $eq: [{ $size: "$staffAvailabilities.timeSlots" }, 0] },
                            then: "Staff not available for given slots",
                            else: {
                                $cond: {
                                    if: { $gt: [{ $size: "$trainerSchedules" }, 0] },
                                    then: "Staff is Busy",
                                    else: "Available"
                                }
                            }
                        }
                    }
                }
            },
            {
                $project: {
                    _id: 1,
                    firstName: 1,
                    lastName: 1,
                    email: 1,
                    isAvailable: 1,
                    reason: 1,
                    "staffAvailabilities.timeSlots": 1
                }
            }
        ]
        console.log(JSON.stringify(pipeline))
        let staffDetails = await this.StaffModel.aggregate(pipeline).exec();
        return {
            message: "Staff Fetched Successfully",
            data: staffDetails
        };
    }

    private calculateAvailableHours(workingHours, notAvailableHours) {
        const availableSlots = [];

        const timeToMinutes = (time) => {
            const [hours, minutes] = time.split(':').map(Number);
            return hours * 60 + minutes;
        };

        const minutesToTime = (minutes) => {
            const hrs = Math.floor(minutes / 60);
            const mins = minutes % 60;
            return `${hrs.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
        };

        for (const workSlot of workingHours) {
            let workStart = timeToMinutes(workSlot.from);
            let workEnd = timeToMinutes(workSlot.to);

            const overlappingNotAvailable = notAvailableHours
                .map(na => ({
                    start: timeToMinutes(na.from),
                    end: timeToMinutes(na.to)
                }))
                .filter(na => na.end > workStart && na.start < workEnd)
                .sort((a, b) => a.start - b.start);

            if (overlappingNotAvailable.length === 0) {
                availableSlots.push({
                    from: workSlot.from,
                    to: workSlot.to
                });
                continue;
            }

            let currentStart = workStart;

            for (const na of overlappingNotAvailable) {
                if (na.start > currentStart) {
                    availableSlots.push({
                        from: minutesToTime(currentStart),
                        to: minutesToTime(na.start)
                    });
                }
                currentStart = Math.max(currentStart, na.end);
            }

            if (currentStart < workEnd) {
                availableSlots.push({
                    from: minutesToTime(currentStart),
                    to: workSlot.to
                });
            }
        }

        return availableSlots;
    }

    private calculateAvailableHoursForBookedRooms(facilityWorkingHours, roomNotAvailHours) {
        const availableSlots = [];

        const timeToMinutes = (time) => {
            const [hours, minutes] = time.split(':').map(Number);
            return hours * 60 + minutes;
        };

        const minutesToTime = (minutes) => {
            const hrs = Math.floor(minutes / 60);
            const mins = minutes % 60;
            return `${hrs.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
        };

        const notAvailable = roomNotAvailHours
            .map(slot => ({
                start: timeToMinutes(slot.from),
                end: timeToMinutes(slot.to),
            }))
            .sort((a, b) => a.start - b.start);

        for (const workSlot of facilityWorkingHours) {
            let workStart = timeToMinutes(workSlot.from);
            const workEnd = timeToMinutes(workSlot.to);
            let currentStart = workStart;

            for (const na of notAvailable) {
                if (na.end <= workStart || na.start >= workEnd) continue;

                if (na.start > currentStart) {
                    availableSlots.push({
                        from: minutesToTime(currentStart),
                        to: minutesToTime(Math.min(na.start, workEnd))
                    });
                }

                currentStart = Math.max(currentStart, na.end);
            }

            if (currentStart < workEnd) {
                availableSlots.push({
                    from: minutesToTime(currentStart),
                    to: minutesToTime(workEnd)
                });
            }
        }

        return availableSlots;
    }

    private filterScheduleByTimeRange(data, timeRanges) {
        const isWithinAnyRange = (time: string) =>
            timeRanges?.some(range => time >= range?.from && time <= range?.to);

        return data?.map(entry => {
            const filteredSchedule = entry?.schedule?.map(scheduleEntry => {
                const filteredSlots = scheduleEntry?.timeSlots?.filter(slot =>
                    isWithinAnyRange(slot?.from) && isWithinAnyRange(slot?.to)
                );

                return filteredSlots?.length > 0
                    ? { ...scheduleEntry, timeSlots: filteredSlots }
                    : null;
            })?.filter(Boolean);

            return filteredSchedule?.length > 0
                ? { ...entry, schedule: filteredSchedule }
                : null;
        })?.filter(Boolean);
    }
}
