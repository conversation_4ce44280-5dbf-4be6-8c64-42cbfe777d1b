import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { RazorpayService } from './razorpay.service';
import { PaymentHistory } from '../schema/payment.schema';
import { CreatePaymentCredentialDto } from '../dto/create-payment-credential.dto';
import { PaymentCredential, PaymentCredentialDocument } from '../schema/paymentCredential.schema';
import * as crypto from 'crypto';
import { CapturePaymentDto } from '../dto/capture-payment.dto';
@Injectable()
export class PaymentService {
    constructor(
        private readonly razorpayService: RazorpayService,
        @InjectModel(PaymentHistory.name) private paymentModel: Model<PaymentHistory>,
        @InjectModel(PaymentCredential.name) private paymentCredentialModel: Model<PaymentCredentialDocument>
    ) { }

    async createPayment(amount: number) {
        const link: any = await this.razorpayService.createPaymentLink(amount);
        // const payment = await this.paymentModel.create({
        //     linkId: link.id,
        //     shortUrl: link.short_url,
        //     amount: link.amount / 100,
        // });

        // return {
        //     linkId: payment.linkId,
        //     url: payment.shortUrl,
        // };
    }

    async getStatus(linkId: string) {
        const payment = await this.paymentModel.findOne({ linkId });
        if (!payment) return { status: 'not_found' };
        return { status: payment.status };
    }

    async updateStatus(linkId: string, status: string) {
        return this.paymentModel.updateOne({ linkId }, { status });
    }
    private IV_LENGTH = 16;

    private generateRandomKey(): Buffer {
        return crypto.randomBytes(32); // AES-256
    }

    private encrypt(text: string): string {
        const key = this.generateRandomKey();
        const iv = crypto.randomBytes(this.IV_LENGTH);
        const cipher = crypto.createCipheriv('aes-256-cbc', key, iv);
        let encrypted = cipher.update(text, 'utf8', 'hex');
        encrypted += cipher.final('hex');


        return [
            key.toString('hex'),
            iv.toString('hex'),
            encrypted
        ].join(':');
    }

    async savePaymentCredential(createPaymentCredentialDto: CreatePaymentCredentialDto, user: any) {
        const isPaymentCredentialExist = await this.paymentCredentialModel.findOne({
            organizationId: user._id,
            paymentGateway: createPaymentCredentialDto.paymentGateway,
        });

        if (isPaymentCredentialExist) {
            throw new BadRequestException('Payment credential already exists for this organization and payment gateway');
        }

        const paymentCredential = await this.paymentCredentialModel.create({
            keyId: this.encrypt(createPaymentCredentialDto.keyId),
            keySecret: this.encrypt(createPaymentCredentialDto.keySecret),
            webhookSecret: createPaymentCredentialDto.webhookSecret
                ? this.encrypt(createPaymentCredentialDto.webhookSecret)
                : undefined,
            paymentGateway: createPaymentCredentialDto.paymentGateway,
            organizationId: user._id,
        });

        return paymentCredential;
    }
    async capturePayment(capturePaymentDto: CapturePaymentDto): Promise<any> {
        const paymentId = capturePaymentDto.paymentId;
        if (!paymentId) {
            throw new BadRequestException('Payment Id is required for the capture operation')
        }
        const paymentCapture = await this.razorpayService.capturePaymentRazorPay(paymentId, capturePaymentDto.amount, capturePaymentDto.organizationId);
        return paymentCapture;
    }
}
