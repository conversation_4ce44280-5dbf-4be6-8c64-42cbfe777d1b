import { BadRequestException, Body, Controller, Post, UseGuards, Query, Get, Param, NotFoundException, Patch, Delete } from "@nestjs/common";
import { ApiBearerAuth, ApiOperation, ApiTags } from "@nestjs/swagger";
import { AuthGuard } from "@nestjs/passport";
import { RolesGuard } from "src/auth/roles.guard";
import { GetUser } from "src/auth/decorators/get-user.decorator";
import { Roles } from "src/auth/decorators/roles.decorator";
import { PaymentService } from '../service/payment.service';
import { CreatePaymentCredentialDto } from '../dto/create-payment-credential.dto'
import { CapturePaymentDto } from "../dto/capture-payment.dto";
import { ENUM_ROLE_TYPE } from "src/role/enums/role.enum";

@ApiTags("Payment-link")
@ApiBearerAuth()
@Controller('payment')
export class PaymentController {
  constructor(private readonly paymentService: PaymentService) { }

  @Post('generate-link')
  @ApiOperation({ summary: "Create a Payment Link" })
  @UseGuards(AuthGuard(), RolesGuard)
  @Roles(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.USER, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN)
  async create(@Body('amount') amount: number) {
    return this.paymentService.createPayment(amount);
  }

  @Get('status')
  async status(@Query('linkId') linkId: string) {
    return this.paymentService.getStatus(linkId);
  }

  @ApiOperation({ summary: "save the credential for the payment gateway" })
  @UseGuards(AuthGuard(), RolesGuard) // Ensure only authenticated and authorized users can access
  @Roles(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.USER, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN)
  @Post("/save-credential")
  async savePaymentCredential(@Body() createPaymentCredentialDto: CreatePaymentCredentialDto, @GetUser() user: any) {
    const response = await this.paymentService.savePaymentCredential(createPaymentCredentialDto, user);
    return {
      message: "Payment credential saved successfully",
      data: response,
    }
  }
  @ApiOperation({ summary: "change the status of the payment from authorization to capture payment" })
  @UseGuards(AuthGuard(), RolesGuard)
  @Roles(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.USER, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN)
  @Post('razorpay/capture')
  async capturePayment(@Body() capturePaymentDto: CapturePaymentDto) {
    const captureReponse = await this.paymentService.capturePayment(capturePaymentDto);
    return {
      message: "Payment captured successfully",
      data: captureReponse
    }
  }
}
