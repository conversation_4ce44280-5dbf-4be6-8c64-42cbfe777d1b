import { Controller, Post, Body, HttpCode, UseGuards, NotFoundException, ForbiddenException, Res, Req } from "@nestjs/common";
import { Response } from "express";
import { Types } from "mongoose";
import { RequestOtpDto } from "../dto/request-otp.dto";
import { AuthService } from "../services/auth.service";
import { VerifyOtpDto } from "../dto/verify-otp.dto";
import { RegisterUserDto } from "../dto/register-user.dto";
import { LoginDto, LoginWithProfileDto } from "../dto/login.dto";
import { ConfigService } from "@nestjs/config";
import { ResetPasswordDto } from "../dto/reset-password.dto";
import { ApiBearerAuth, ApiTags, ApiOperation, ApiResponse } from "@nestjs/swagger";
import { SetPasswordDto } from "../dto/set-password.dto";
import { ChangePasswordDto } from "../dto/change-password.dto";
import { AuthGuard } from "@nestjs/passport";
import { RolesGuard } from "../roles.guard";
import { Roles } from "../decorators/roles.decorator";
import { ENUM_ROLE_TYPE } from "src/role/enums/role.enum";
import { GetUser } from "../decorators/get-user.decorator";
import { SessionService } from "src/session/services/session.service";
import { IRequestApp } from "src/common/request/interfaces/request.interface";
import { AuthLoginResponseDto } from "../dto/response/auth.login.response.dto";
import { Session } from "src/session/decorators/session.decorator";
import { Cookies } from "src/common/decorators/cookies.decorator";
import { IUserDocument } from "src/users/interfaces/user.interface";
import { IDatabaseObjectId } from "src/common/database/interfaces/database.objectid.interface";
import { RefreshSessionRequestDto } from "../dto/refresh.session.request.dto";
import { AuthJwtAccessProtected } from "../decorators/auth.jwt.decorator";
import { GetOrganizationId } from "src/organization/decorators/organization.decorator";
import { UserService } from "src/users/services/user.service";
import { HelperDateService } from "src/common/helper/services/helper.date.service";

@ApiTags("Authentication")
@ApiBearerAuth()
@Controller("auth")
export class AuthController {
    constructor(
        private authService: AuthService,
        private readonly configService: ConfigService,
        private readonly sessionService: SessionService,
        private readonly userService: UserService,
        private readonly helperDateService: HelperDateService
    ) { }

    @Post("/request-otp")
    @HttpCode(200)
    @ApiOperation({ summary: "Request OTP for authentication" })
    async requestOTP(@Body() requestOtpDto: RequestOtpDto) {
        const createOtp = await this.authService.createOtp(requestOtpDto);
        return {
            message: "OTP generated",
            data: {
                otp: createOtp,
            },
        };
    }

    @Post("/verify-otp")
    @HttpCode(200)
    @ApiOperation({ summary: "Verify the OTP" })
    async verifyOTP(@Body() verifyOtpDto: VerifyOtpDto) {
        const verifyOtp = await this.authService.verifyOtp(verifyOtpDto);
        return verifyOtp;
    }

    @Post("/v2/verify-otp/")
    @HttpCode(200)
    @ApiOperation({ summary: "Verify the OTP" })
    async verifyOTPv2(@Body() verifyOtpDto: VerifyOtpDto) {
        const verifyOtp = await this.authService.verifyOtpV2(verifyOtpDto);
        return verifyOtp;
    }

    @Post("/register")
    @HttpCode(201)
    @ApiOperation({ summary: "Register a new user" })
    async registerUser(@Body() registerUserDto: RegisterUserDto, @Req() request: IRequestApp, @Res({ passthrough: true }) response: Response): Promise<object> {
        const data = await this.authService.registerUser(registerUserDto);
        const sessionId = await this.authService.startNewSession(request, data.user);
        await this.authService.setCookieData(response, data.user, sessionId.toString(), data.accessToken, data.loginDate, data.organizationId);
        return {
            message: "User registered",
            data: data,
        };
    }

    @Post('refresh-session')
    @HttpCode(200)
    @ApiOperation({ summary: 'Refresh user session' })
    @AuthJwtAccessProtected()
    async refreshSession(
        @Req() request: IRequestApp,
        @GetUser() user: IUserDocument,
        @Body() body: RefreshSessionRequestDto,
        @GetOrganizationId() organizationId: IDatabaseObjectId,
        @Res() response: Response // Remove passthrough to handle response directly
    ) {
        // Validate and create/update session
        const staff = await this.authService.validatePin2(body.pin, organizationId);

        const isDelegateUser = staff.userId.toString() !== user._id.toString();

        // Get the session user
        const sessionUser = await this.userService.findOneWithRoleAndPermissions({ _id: new Types.ObjectId(staff.userId) });

        // Create delegate session
        const sessionId = await this.authService.startDelegateSession(request, sessionUser);

        // Generate token data for cookies
        const tokenData = await this.authService.createToken(sessionUser, organizationId);
        const loginDate = this.helperDateService.create();

        // Set cookies in the response
        await this.authService.setCookieData(
            response,
            sessionUser,
            sessionId.toString(),
            tokenData,
            loginDate,
            organizationId.toString()
        );

        // Process policies for response
        const allPolicies = [...sessionUser.role.policies, ...sessionUser.assignedPolicies];
        const restrictedPolicyIds = new Set(sessionUser.restrictedPolicies?.map(policy => policy._id.toString()));
        const policies = allPolicies.filter(policy => !restrictedPolicyIds.has(policy._id.toString()));

        // Clean up sensitive data
        sessionUser.role.policies = undefined;
        sessionUser.password = undefined;
        sessionUser.salt = undefined;
        sessionUser.assignedPolicies = undefined;
        sessionUser.restrictedPolicies = undefined;

        // Prepare response data
        const responseData = {
            message: "User logged in successfully with PIN",
            data: {
                user: sessionUser,
                policies: policies,
                roleType: sessionUser.role.type,
                userSessionId: sessionId,
                isDelegateUser
            }
        };

        // Send the response manually
        return response.status(200).json(responseData);
    }

    @Post('/login')
    @HttpCode(200)
    @ApiOperation({ summary: 'Login' })
    @ApiResponse({
        status: 200,
        description: 'User Login successful',
        type: AuthLoginResponseDto,
    })
    async login(
        @Body() loginDto: LoginDto,
        @Req() request: IRequestApp,
        @Res() response: Response, // Remove passthrough to handle response directly
    ) {
        const data = await this.authService.login(loginDto);

        // Validate and create/update session
        const session = await this.authService.startNewSession(request, data.user);

        // Set cookies in the response
        await this.authService.setCookieData(
            response,
            data.user,
            session._id.toString(),
            data,
            data.loginDate,
            data?.organizationId?.toString()
        );

        data.user.role.policies = undefined
        data.user.assignedPolicies = undefined
        data.user.restrictedPolicies = undefined

        // Prepare response data
        const responseData = {
            message: 'User Login successful',
            data: {
                user: {
                    ...data.user.toJSON(),
                },
                roleType: data.roleType,
                accessToken: data.accessToken,
                tokenType: data.tokenType,
                loginDate: data.loginDate,
                organizationId: data.organizationId,
                session: session._id
            } as AuthLoginResponseDto
        };

        // Send the response manually
        return response.status(200).json(responseData);
    }

    @Post("/login/trainer")
    @HttpCode(200)
    @ApiOperation({ summary: "Login a trainer" })
    async loginTrainer(@Body() loginDto: LoginDto) {
        const data = await this.authService.login(loginDto);
        if (data?.user?.role.type !== ENUM_ROLE_TYPE.TRAINER) {
            throw new NotFoundException({ message: "You are not registered as trainer" })
        }
        return {
            message: "User Login",
            data: data,
        };
    }

    @Post("/login/user")
    @HttpCode(200)
    @ApiOperation({ summary: "Login a user" })
    async loginUser(@Body() loginDto: LoginDto) {
        const data = await this.authService.login(loginDto);
        if (data?.user?.role.type !== ENUM_ROLE_TYPE.USER) {
            throw new NotFoundException({ message: "User not found" })
        }
        return {
            message: "User Login",
            data: data,
        };
    }

    @Post("/profile/login")
    @HttpCode(200)
    @ApiOperation({ summary: "Login a user" })
    async loginWithProfile(@Body() loginDto: LoginWithProfileDto) {
        const data = await this.authService.loginWithProfile(loginDto);
        return {
            message: "User Login",
            data: data,
        };
    }

    @Post("/change-password")
    @HttpCode(200)
    @ApiOperation({ summary: "Change user password" })
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER, ENUM_ROLE_TYPE.USER, ENUM_ROLE_TYPE.WEB_MASTER)
    async changePassword(@GetUser() user, @Body() changePasswordDto: ChangePasswordDto) {
        let password = await this.authService.updatePassword(changePasswordDto, user);
        return {
            message: "Password updated",
            data: "",
        };
    }

    @Post("/forget-password-request-otp")
    @HttpCode(200)
    @ApiOperation({ summary: "Request OTP for password reset" })
    async forgetPasswordRequestOtp(@Body() requestOtpDto: RequestOtpDto) {
        const createOtp = await this.authService.forgetPasswordOtp(requestOtpDto);
        return {
            message: "OTP generated",
            data: {
                otp: createOtp,
            },
        };
    }

    @Post("/reset-password")
    @HttpCode(200)
    @ApiOperation({ summary: "Reset user password" })
    async resetPassword(@Body() resetPasswordDto: ResetPasswordDto) {
        const createOtp = await this.authService.resetPassword(resetPasswordDto);
        return {
            message: "Password reset successfully",
            data: {
                otp: createOtp,
            },
        };
    }

    @Post("/set-password")
    @HttpCode(200)
    @ApiOperation({ summary: "set user password" })
    async setPassword(@Body() setPasswordDto: SetPasswordDto) {
        const result = await this.authService.setPassword(setPasswordDto);
        return {
            message: "Password set successfully",
            data: result,
        };
    }

    @Post("/logout")
    @HttpCode(200)
    @ApiOperation({ summary: "Logout the user" })
    async logout(
        @Req() request: IRequestApp,
        @Res() response: Response, // Remove passthrough to handle response directly
        @Session('userSessionId') sessionId: string
    ) {
        if (sessionId) {
            // Revoke the session in database
            const session = await this.sessionService.findOneById(new Types.ObjectId(sessionId));
            if (session) {
                await this.sessionService.updateRevoke(session);
            }

            // Clear the session from cache
            await this.sessionService.deleteLoginSession(new Types.ObjectId(sessionId));
        }

        // Clear cookies by setting them with an expired date
        const cookieNames = [
            'userId',
            'userSessionId',
            'loginDate',
            'roleType',
            'accessToken',
            'tokenType',
            'organizationId'
        ];

        // Get session name from config
        const sessionName = this.configService.get<string>('auth.session.name');
        cookieNames.push(sessionName);

        // Clear cookies by setting them with an expired date and proper attributes
        cookieNames.forEach(cookieName => {
            // Format the cookie string manually to ensure attributes are set correctly
            const cookieString = `${cookieName}=; Max-Age=0; Path=/; HttpOnly; Secure; SameSite=None; Expires=Thu, 01 Jan 1970 00:00:00 GMT`;

            // Append the cookie to the response headers
            response.append('Set-Cookie', cookieString);
        });

        // Destroy express session
        await new Promise<void>((resolve, reject) => {
            request.session.destroy((err: any) => {
                if (err) reject(err);
                resolve();
            });
        });

        // Send the response manually
        return response.status(200).json({
            message: "User logged out successfully",
        });
    }

}
