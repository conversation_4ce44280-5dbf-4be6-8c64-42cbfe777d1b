import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, ValidateIf, IsEmail, Length, IsNotEmpty, IsObject, IsOptional } from "class-validator";
import { AuthTypes } from "../../utils/enums/auth.enum";
import { Transform } from "class-transformer";
import { ApiProperty } from "@nestjs/swagger";
import { Types } from "mongoose";

export class LoginDto {
    @ApiProperty({
        description: "The authentication type. Can be either EMAIL or MOBILE.",
        enum: AuthTypes,
        example: AuthTypes.EMAIL,
    })
    @IsNotEmpty({ message: "Authentication type is required. Can be either email or mobile." })
    @IsEnum([AuthTypes.EMAIL, AuthTypes.MOBILE], { message: "Authentication type must be either EMAIL or MOBILE." })
    type: string;

    @ApiProperty({
        description: "The user email. Required if the authentication type is EMAIL.",
        example: "<EMAIL>",
        maxLength: 255,
        required: false,
    })
    @ValidateIf((req) => req.type === AuthTypes.EMAIL)
    @Transform((param) => param.value.toLowerCase())
    @IsEmail({}, { message: "A valid email address is required." })
    @MaxLength(255, { message: "Email must not exceed 255 characters." })
    email: string;

    @ApiProperty({
        description: "The user mobile number. Required if the authentication type is MOBILE.",
        example: "9876543210",
        //minLength: 10,
        //maxLength: 10,
        required: false,
    })
    @IsOptional()
    @ValidateIf((req) => req.type === AuthTypes.MOBILE)
    //@Length(10, 10, { message: "Mobile number must be exactly 10 digits." })
    mobile: string;

    @ApiProperty({
        description: "The user password. Required for both EMAIL and MOBILE authentication types.",
        example: "Demo@123",
    })
    @IsNotEmpty({ message: "Password is required." })
    password: string;
}

export class LoginWithProfileDto {
   
    

    @ApiProperty({
        description: "The token received from the OTP verification.",
        example: "123456",
    })
    @IsNotEmpty({ message: "Token is required." })
    token: string;

    @ApiProperty({
        description: "The user email. Required if the authentication type is EMAIL.",
        example: new Types.ObjectId(),
        maxLength: 255,
        required: false,
    })
    @IsNotEmpty({ message: "ProfileId is required." })
    profile?: string;
}
