import { DynamicModule, Global, Module } from "@nestjs/common";
import { AuthService } from "./services/auth.service";
import { AuthController } from "./controllers/auth.controller";
import { MongooseModule } from "@nestjs/mongoose";
import { Otp, OtpSchema } from "./schemas/otp.schema";
import { UsersModule } from "src/users/users.module";
import { JwtModule } from "@nestjs/jwt";
import { PassportModule } from "@nestjs/passport";
// import { JwtStrategy } from "./strategies-ex/jwt.strategy";
import { HttpModule } from "@nestjs/axios";
import { UtilsModule } from "src/utils/utils.module";
import { ConfigModule, ConfigService } from "@nestjs/config";
import { MailModule } from "src/mail/mail.module";
import { DATABASE_PRIMARY_CONNECTION_NAME } from "src/common/database/constants/database.constant";
import { AuthJwtAccessStrategy } from "./guards/jwt/strategies/auth.jwt.access.strategy";
import { RoleModule } from "src/role/role.module";
import { SessionModule } from "src/session/session.module";
import { Msg91Module } from "src/message/message.module";

@Global()
@Module({
    imports: [
        PassportModule.register({
            defaultStrategy: "jwt",
        }),

        JwtModule.registerAsync({
            imports: [ConfigModule],
            useFactory: async (configService: ConfigService) => ({
                global: true,
                secret: configService.get<string>("JWT_SECRET"),
                signOptions: { expiresIn: "189h" },
            }),
            inject: [ConfigService],
        }),
        MongooseModule.forFeature([{ name: Otp.name, schema: OtpSchema }], DATABASE_PRIMARY_CONNECTION_NAME),
        UsersModule,
        HttpModule,
        UtilsModule,
        MailModule,
        RoleModule,
        SessionModule,
        Msg91Module
    ],
    // providers: [AuthService, JwtStrategy,AuthService],
    providers: [AuthService, AuthJwtAccessStrategy,AuthService],
    controllers: [AuthController],
    // exports: [PassportModule, JwtStrategy, AuthService],
    exports: [PassportModule, AuthJwtAccessStrategy, AuthService],
})
export class AuthModule {
    static forRoot(): DynamicModule {
        return {
            module: AuthModule,
            providers: [],
            exports: [],
            controllers: [],
            imports: [],
        };
    }
}
